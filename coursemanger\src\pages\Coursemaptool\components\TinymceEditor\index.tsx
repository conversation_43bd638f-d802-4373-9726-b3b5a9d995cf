import { uploadFile } from "@/api/homework";
import useLocale from "@/hooks/useLocale";
import React ,{useEffect,useState,useRef} from "react";
import { useBus, useListener } from 'react-bus';
interface Props {
    id: string;
    nodeid: string;
    value?: string;
    onChange?: (value: string) => void;
}

const DEFAULT_TYPES = "jpeg,jpg,jpe,jfi,jif,jfif,png,gif,bmp,webp";
const IMAGE_TYPES = `${DEFAULT_TYPES},${DEFAULT_TYPES.toUpperCase()}`;

const TinymceEditor = ({id,value,nodeid,onChange}:Props) => {
  const editref = useRef<any>(null);
  const bus = useBus();
  const { t } = useLocale();

  const handleContentChange = (editor: any) => {
    const content = editor.getContent();
    onChange && onChange(content);
    bus.emit('updatanode', {
      id: nodeid,
      data: {
        explanation: content || ''
      }
    });
  };

 // 单图上传图片
 const images_upload_handler = (blobInfo: any, succFun: any, failFun: any) => {
    const formData = new FormData();
    formData.append('file', blobInfo.blob(), blobInfo.filename());
    uploadFile(formData).then((res: any) => {
      if (res.success) {
        succFun(res.data);
      } else {
        failFun(res.error?.title || t("上传失败"));
      }
    });
  };
  const init = () => {
    (window as any).tinymce.editors[id]?.destroy();
    (window as any).tinymce.init({
        selector: `#${id}`,
        toolbar: [
            'undo redo | forecolor backcolor bold italic underline strikethrough | alignleft aligncenter alignright alignjustify| | bullist numlist | emoticons charmap',
            'formatselect fontselect fontsizeselect table image link sobeymath',
            // "formatselect fontselect fontsizeselect table image"
          ],
        language:'zh_CN',
        menubar: '',
        height: 300,
        plugins: 'sobeymath paste table image link',
        valid_elements: '*[*]',
        paste_data_images:true,
        paste_as_text: true, // 粘贴为纯文本
        images_upload_handler: images_upload_handler, //图片上传
        images_file_types: IMAGE_TYPES,
        relative_urls: false,
        convert_urls: false,
        image_caption: true,
        // 初始化完成
        init_instance_callback: (editor:any) => {
            editref.current = editor;
            editor.setContent(value || '');

            // 添加粘贴事件监听
            editor.on('paste', () => {
                setTimeout(() => {
                    handleContentChange(editor);
                }, 100);
            });

            // 添加撤销和重做事件监听
            editor.on('Undo', () => {
                setTimeout(() => {
                    handleContentChange(editor);
                }, 100);
            });

            editor.on('Redo', () => {
                setTimeout(() => {
                    handleContentChange(editor);
                }, 100);
            });

            // 添加删除和剪切事件监听
            editor.on('cut', () => {
                setTimeout(() => {
                    handleContentChange(editor);
                }, 100);
            });

            editor.on('keyup', (e: any) => {
                // 处理删除键和退格键
                if (e.keyCode === 46 || e.keyCode === 8) {
                    handleContentChange(editor);
                }
            });

            editor.on('change', () => {
                handleContentChange(editor);
            });

            editor.on('input', () => {
                handleContentChange(editor);
            });
        }
    });
  }

  useEffect(() => {
    console.log('init');
    init();
    return () => {
        (window as any).tinymce.editors[id]?.destroy();
    };
  }, [nodeid]);

  return <div id={id} ></div>
}


export default TinymceEditor;
