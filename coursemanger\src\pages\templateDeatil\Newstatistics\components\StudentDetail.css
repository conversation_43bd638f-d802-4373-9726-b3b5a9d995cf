.stu-detail-container {
  overflow: auto;
  height: 100%;
  background: #fff;
}
.stu-detail-container .back-wrp {
  border-bottom: 1px solid #E3E3E3;
  padding: 6px 0;
}
.stu-detail-container .back-wrp button {
  font-size: 16px;
}
@media screen and (max-width: 768px) {
  .stu-detail-container .info-wrp {
    display: flex;
    flex-wrap: wrap;
    align-items: end;
  }
  .stu-detail-container .col {
    flex: 1 1 40%;
    margin-bottom: 6px;
  }
}
.stu-detail-container .info-wrp {
  font-size: 16px;
  padding: 20px 30px;
  padding-right: 0;
  display: flex;
  justify-content: space-around;
  align-items: end;
  background-color: var(--primary-bg-color);
  margin: 10px 15px;
  margin-bottom: 6px;
}
.stu-detail-container .info-wrp > .col {
  font-size: 14px;
}
.stu-detail-container .info-wrp > .col.user {
  text-align: left;
  display: flex;
  align-items: flex-start;
  margin-right: 50px;
}
.stu-detail-container .info-wrp > .col.user img {
  height: 18px;
  width: auto;
  display: inline-block;
  margin-right: 10px;
  margin-top: 12px;
}
.stu-detail-container .info-wrp > .col .anticon {
  color: var(--primary-color);
  margin-right: 10px;
}
.stu-detail-container .info-wrp > .col .bold {
  font-size: 24px;
  font-weight: 600;
  color: #333333;
}
.stu-detail-container .info-wrp > .col .label {
  font-size: 16px;
}
.stu-detail-container .info-wrp > .col .label + div {
  padding-left: 25px;
}
.stu-detail-container .info-wrp > .col .gray {
  font-size: 14px;
  color: #818385;
}
.stu-detail-container .tab-wrp {
  padding-left: 30px;
  margin-bottom: 16px;
  margin-top: 20px;
}
.stu-detail-container .tab-wrp .tab-item {
  padding: 0 16px;
  display: inline-block;
  border-radius: 30px;
  margin-right: 10px;
  cursor: pointer;
  height: 30px;
  line-height: 30px;
}
.stu-detail-container .tab-wrp .tab-item.active {
  background: var(--primary-color);
  color: #fff;
}
.stu-detail-container .table-wrp {
  padding: 0 30px;
}
.stu-detail-container .row {
  border-bottom: 1px solid #EDEDED;
  padding: 40px 0;
}
.stu-detail-container .row .header {
  width: 100%;
  height: 23px;
  line-height: 23px;
  border-left: 4px solid var(--primary-color);
  display: flex;
  align-items: center;
  padding-left: 10px;
  padding-right: 15px;
  font-size: 18px;
  font-weight: 600;
}
.stu-detail-container .row .header div:nth-child(2) {
  margin-left: auto !important;
}
.stu-detail-container .row .header .ant-btn {
  margin-left: 20px;
}
.stu-detail-container .row .header .ant-select {
  width: 130px;
  margin-left: 20px;
}
@media screen and (max-width: 768px) {
  .stu-detail-container .row .header {
    display: revert;
  }
  .stu-detail-container .row .header .headertilie {
    margin-bottom: 10px;
  }
  .stu-detail-container .row .header .headerSelect div {
    margin-left: 10px !important;
  }
  .stu-detail-container .row .header .headerSelect .ant-select {
    margin-left: 10px;
    margin-bottom: 10px;
  }
  .stu-detail-container .row .situation {
    margin-top: 98px;
  }
}
.stu-detail-container .row .chart_detail_box {
  display: flex;
  justify-content: space-between;
}
.stu-detail-container .row .chart_detail_box .top_left {
  width: 45%;
  height: 320px;
  margin-top: 20px;
}
.stu-detail-container .row .chart_detail_box .top_right {
  width: 45%;
  height: 320px;
  margin-top: 30px;
  padding-right: 20px;
  overflow: auto;
}
.stu-detail-container .row .chart_detail_box .top_right::-webkit-scrollbar {
  display: none;
  /* 隐藏滚动条 */
}
.stu-detail-container .row .chart_detail_box .top_right .top_right_container {
  height: auto;
}
.stu-detail-container .row .chart_detail_box .top_right .top_right_container .header_inner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 7px 0px;
}
.stu-detail-container .row .chart_detail_box .top_right .top_right_container .header_inner .text .text1 {
  background-color: #E5F5FF;
  color: #76A2D2;
  padding: 1px 6px;
  border-radius: 5px;
  font-size: 15px;
  display: inline-block;
  min-width: 60px;
  text-align: center;
  max-width: 100px;
  text-wrap: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.stu-detail-container .row .chart_detail_box .top_right .top_right_container .header_inner .text .active {
  background-color: #579bff;
  color: white;
}
.stu-detail-container .row .chart_detail_box .top_right .top_right_container .header_inner .text .text2 {
  font-size: 14px;
  margin-left: 15px;
  display: inline-block;
  max-width: 500px;
  text-wrap: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.stu-detail-container .row .chart_detail_box .top_right .top_right_container .header_inner .end {
  color: #76A2D2;
  font-size: 13px;
  cursor: pointer;
}
.stu-detail-container .row .chart_detail_box .top_right .top_right_container .header_inner .end img {
  width: 7px;
  height: 7px;
  margin: 7px 0px 0px 8px;
}
.stu-detail-container .row .chart_detail_box .top_right .top_right_container .detail {
  height: 160px;
  overflow: hidden;
  transition: height 0.3s ease;
}
.stu-detail-container .row .chart_detail_box .top_right .top_right_container .detail .echarts-for-react {
  height: 160px!important;
}
.stu-detail-container .row .chart_detail_box .top_right .top_right_container .hidden {
  height: 0;
}
.stu-detail-container .row .pie_chart .antd_space {
  margin-bottom: 20px;
  border-left: 4px solid var(--primary-color);
  padding-left: 10px;
}
.stu-detail-container .row .pie_chart .antd_space .btn {
  position: relative;
  font-size: 15px;
  font-weight: 700;
  cursor: pointer;
}
.stu-detail-container .row .pie_chart .antd_space .active {
  color: #559bf2;
}
.stu-detail-container .row .pie_chart .antd_space .active:after {
  content: "";
  display: block;
  width: 50%;
  height: 2px;
  background-color: #559bf2;
  /* 设置横线颜色 */
  position: absolute;
  bottom: -10px;
  left: 25%;
}
@media screen and (max-width: 768px) {
  .stu-detail-container .chart_box_study_content {
    display: flex;
    flex-direction: column;
  }
  .stu-detail-container .chart_box_study_content > div {
    width: 100%;
  }
  .stu-detail-container .chart_box_study_content > div:nth-child(1) {
    width: 100%;
  }
  .stu-detail-container .chart_box_study_content > div:nth-child(2) {
    width: 100%;
  }
}
.stu-detail-container .chart_box_study_content {
  display: flex;
}
.stu-detail-container .chart_box_study_content > div {
  width: 0;
}
.stu-detail-container .chart_box_study_content > div:nth-child(1) {
  flex: 2;
}
.stu-detail-container .chart_box_study_content > div:nth-child(2) {
  flex: 5;
}
