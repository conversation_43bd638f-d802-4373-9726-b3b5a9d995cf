import { useState, useEffect } from 'react';
import { debounce } from 'lodash';  // 需要先安装 lodash

export const useIsPhone = () => {
  const [isPhone, setIsPhone] = useState(window.innerWidth <= 768);

  useEffect(() => {
    // 使用防抖处理resize事件
    const handleResize = debounce(() => {
      setIsPhone(window.innerWidth <= 768);
    }, 200);  // 200ms 延迟

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      handleResize.cancel();  // 清理防抖
    };
  }, []);

  return isPhone;
};
