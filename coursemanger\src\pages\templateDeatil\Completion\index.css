.completion_container {
  background-color: #dceefc;
  min-height: 100%;
  min-width: 1600px;
  padding: 15px;
  border-radius: 8px;
}
.completion_container .top {
  display: flex;
  justify-content: space-between;
  background-color: white;
  padding: 20px 30px;
  border-radius: 8px;
  position: relative;
}
.completion_container .top .top_title {
  position: absolute;
  left: 12px;
  top: 12px;
  z-index: 10;
}
.completion_container .top .top_select {
  position: absolute;
  left: 140px;
  top: 8px;
  min-width: 150px;
  z-index: 10;
}
.completion_container .top .top_fresh_time {
  position: absolute;
  right: 60px;
  top: 5px;
  z-index: 10;
}
.completion_container .top .top_fresh_time .refresh_btn {
  margin-right: 10px;
}
.completion_container .top .top_left {
  width: 45%;
  height: 320px;
  margin-top: 20px;
  position: relative;
}
.completion_container .top .top_right {
  width: 45%;
  height: 320px;
  padding-top: 30px;
  overflow: auto;
}
.completion_container .top .top_right::-webkit-scrollbar {
  display: none;
  /* 隐藏滚动条 */
}
.completion_container .top .top_right .top_right_title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  font-size: 20px;
  font-weight: 600;
}
.completion_container .top .top_right .top_right_container {
  height: auto;
}
.completion_container .top .top_right .top_right_container .header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 7px 0px;
}
.completion_container .top .top_right .top_right_container .header .text {
  display: flex;
  align-items: center;
}
.completion_container .top .top_right .top_right_container .header .text .text1 {
  background-color: #e5f5ff;
  color: #76a2d2;
  padding: 1px 6px;
  border-radius: 5px;
  font-size: 15px;
  display: inline-block;
  min-width: 60px;
  text-align: center;
  max-width: 100px;
  text-wrap: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.completion_container .top .top_right .top_right_container .header .text .active {
  background-color: #579bff;
  color: white;
}
.completion_container .top .top_right .top_right_container .header .text .text2 {
  font-size: 14px;
  margin-left: 15px;
  display: inline-block;
  max-width: 500px;
  text-wrap: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.completion_container .top .top_right .top_right_container .header .end {
  width: 70px;
  color: #76a2d2;
  font-size: 13px;
  cursor: pointer;
}
.completion_container .top .top_right .top_right_container .header .end img {
  width: 7px;
  height: 7px;
  margin: 7px 0px 0px 8px;
}
.completion_container .top .top_right .top_right_container .detail {
  height: 160px;
  overflow: hidden;
  transition: height 0.3s ease;
}
.completion_container .top .top_right .top_right_container .detail .echarts-for-react {
  height: 160px !important;
}
.completion_container .top .top_right .top_right_container .hidden {
  height: 0;
}
.completion_container .center {
  margin: 15px 0px;
  width: 100%;
  height: 360px;
  background-color: white;
  padding: 20px 30px;
  border-radius: 8px;
}
.completion_container .center .mode_switch_wrapper {
  float: right;
  display: flex;
  align-items: center;
}
.completion_container .center .mode_switch_wrapper .mode_switch {
  cursor: pointer;
  font-size: 16px;
  margin-left: 10px;
}
.completion_container .center .mode_switch_wrapper .mode_switch .active,
.completion_container .center .mode_switch_wrapper .mode_switch:hover {
  color: var(--primary-color);
}
.completion_container .centerTwo {
  margin: 15px 0px;
  width: 100%;
  height: 400px;
  background-color: white;
  padding: 20px 30px;
  padding-top: 35px;
  border-radius: 8px;
}
.completion_container .bottom {
  width: 100%;
  background-color: white;
  padding: 20px 30px;
  border-radius: 8px;
}
.completion_container .bottom .filter {
  margin-bottom: 10px;
}
.completion_container .bottom .aaa {
  background: #f4fafe;
}
.completion_container .bottom .ant-table-wrapper .ant-table-thead .table-header-cell {
  background-color: rgba(53, 158, 255, 0.1);
}
.completion_container .bottom .ant-pagination-item-active {
  border-color: #549cff;
}
.completion_container .bottom .ant-pagination-item-active a {
  color: #549cff;
}
.completion_container .completion_empty {
  height: 100%;
  border-radius: 8px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}
.completion_container .completion_empty .empty-header {
  height: 100%;
  font-size: 16px;
  position: absolute;
  left: 0;
  top: 0;
}
