import React, { useEffect, useState } from 'react';
import './index.less'
import ResourceModal from '@/components/ResourceModal';
import { getTreebylevel } from '@/api/addCourse';
import { getRoucosePoint } from '@/api/coursemap'
import { message, Modal } from 'antd';
import { createNodeoredge, traversalTree } from '../../Editmap/util';
import useLocale from '@/hooks/useLocale';
const { confirm } = Modal;
interface SelectResourcesProps {
  visible: boolean;
  addtype:number;
  selectnode:any,
  graph:any,
  onCancel: () => void;
  onOk: (newdata:any) => void;
}

const SelectResources: React.FC<SelectResourcesProps> = ({
  visible,
  addtype,
  selectnode,
  graph,
  onCancel,
  onOk,
}) => {
  // 选择资源弹窗的数据
  const [modalTreeData, setModalTreeData] = useState<ResourceModal.treeItem[]>([]);
  // 选择资源弹窗的可见性
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const { t } = useLocale();
   //显示Modal
   const showModal = () => {
    const getTreeData = () => {
      getTreebylevel().then(res => {
        if (res && res.success) {
          let treeData = forTree(res.data, []);
          setModalTreeData(treeData);
          setTimeout(() => {
            setModalVisible(true);
          }, 100);
        } else {
          console.error(res);
        }
      });
    };
    // 遍历目录树
    const forTree = (tree: any, parentsKeys: string[]) => {
      return tree.map((item: any) => {
        return {
          key: item.id,
          parentsKeys,
          title: item.name,
          path: item.path,
          children: item.children
            ? forTree(item.children, [...parentsKeys, item.code])
            : [],
        };
      });
    };
    getTreeData();
  };

  useEffect(() => {
    if(visible){
      showModal();
    }
  }, [visible]);

  const handleOk = (e:any[]) => {
    getRoucosePoint({
      "courseId": null,
      "courseNo": null,
      "courseName": null,
      "semester": null,
      "week": null,
      "videoIds": e.map((item:any) => item.contentId) || []   // ['3abaff5d34f34f64b2046c3afb2540fe'] 
  }).then((res:any) => {
      if(res && res.data.success){  
        if(addtype  === 0){
          const rootnodes = graph.getRootNodes();
          if(rootnodes.length === 0){
            message.error('请先创建根节点');
            return;
          }
          const newdata = traversalTree(res.data.data.videoKnowledgeGraphs, rootnodes[0]);        
          onOk(newdata);
        }else{
          if(!selectnode){
            message.error('请选中一个节点');
            return;
          }
          const newdata = createNodeoredge(res.data.data.videoKnowledgeGraphs, selectnode);        
          onOk(newdata);          
        }     
      }else{
        message.error(res.data.error.title);
      }
    })
  };

  // 确认提示框
  const handleConfirm = (e:any[]) => {
    confirm({
      title: t(`导入数据会覆盖当前编辑内容，是否确定？`),
      onOk() {
        handleOk(e);
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  }
  
  return (
    <ResourceModal        
        treeData={modalTreeData}
        visible={modalVisible}
        selectMode="folder"
        fileType={['biz_sobey_video']}
        onConfirm={(e)=>{
          if(addtype === 0){
            handleConfirm(e);
          }else{
            handleOk(e);
          }
        }} 
        onShowDetail={(e: any) => {
          
        }}    
        onCancel={onCancel}        
        // fileType={['biz_sobey_video','biz_sobey_audio','biz_sobey_picture','biz_sobey_document']}
        multi={true}
        />
  );
};

export default SelectResources;
