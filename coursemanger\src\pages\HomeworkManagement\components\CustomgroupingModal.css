.Custom_modal .ant-modal-header .tp-select-header {
  color: #999;
  display: flex;
  align-items: center;
}
.Custom_modal .ant-modal-header .tp-select-header .ant-input {
  margin: 0 5px;
  height: 30px;
  border-radius: 0;
  width: 80px;
}
.Custom_modal .ant-modal-content .ant-modal-body {
  padding: 0;
}
.Custom_modal .ant-modal-content .ant-modal-body .Custom-box .btn-box {
  max-height: 403px;
  min-height: 400px;
  display: flex;
}
.Custom_modal .ant-modal-content .ant-modal-body .Custom-box .btn-box .box-left {
  width: 30%;
  border-right: 2px solid #eee;
}
.Custom_modal .ant-modal-content .ant-modal-body .Custom-box .btn-box .box-left .left-list {
  max-height: 350px;
  overflow: auto;
}
.Custom_modal .ant-modal-content .ant-modal-body .Custom-box .btn-box .box-left .left-list .group-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  margin: 6px 0;
  border-radius: 4px;
  transition: all 0.3s;
}
.Custom_modal .ant-modal-content .ant-modal-body .Custom-box .btn-box .box-left .left-list .group-item.active {
  background: #f5f5f5 !important;
}
.Custom_modal .ant-modal-content .ant-modal-body .Custom-box .btn-box .box-left .left-list .delete-icon {
  color: #000000;
  padding: 4px;
  transition: all 0.3s;
}
.Custom_modal .ant-modal-content .ant-modal-body .Custom-box .btn-box .box-right {
  width: 70%;
}
.Custom_modal .ant-modal-content .ant-modal-body .Custom-box .btn-box .box-right .right-list .right-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.Custom_modal .ant-modal-content .ant-modal-body .Custom-box .btn-box .box-right .right-list .right-item {
  padding: 10px 20px;
  background-color: #f6f6f6;
}
.Custom_modal .ant-modal-content .ant-modal-body .Custom-box .btn-box .box-right .right-list .right-item .itemright {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.Custom_modal .ant-modal-content .ant-modal-body .Custom-box .btn-box .box-right .right-list .itmebox {
  max-height: 300px;
  overflow: auto;
}
.Custom_modal .ant-modal-content .ant-modal-body .Custom-box .btn-box .box-right .right-list .itmeList {
  padding: 10px 20px;
  color: var(--primary-color);
}
.Custom_modal .ant-modal-content .ant-modal-body .Custom-box .btn-box .box-right .right-list .itemrow {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.Custom_modal .ant-modal-content .group-itemList {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  margin: 6px 0;
  border-radius: 4px;
  transition: all 0.3s;
  cursor: pointer;
}
.Custom_modal .ant-modal-content .group-itemList.active {
  background: #f5f5f5 !important;
}
.Custom_modal .ant-table-pagination.ant-pagination {
  margin: 16px 10px;
}
