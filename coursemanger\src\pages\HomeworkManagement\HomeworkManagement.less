.homework-container {
  width: 100%;
  height: 100%;
  background: #fff;
  overflow: auto;

  .ant-spin, .ant-spin-nested-loading {
    width: 100%;
    height: 100%;
  }

  .ant-spin-container {
    width: 100%;
    height: 100%;
  }

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #cecece;
    border-radius: 99px;
  }

  .tree-container {
    height: 100%;
    padding: 20px;
  }

  .homework-header-btn {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;

    .download-btn {
      cursor: pointer;
      color: var(--primary-color);

    }

    .title {
      font-size: 16px;
      // font-weight: bold;
    }

    .export-btn {
      color: var(--primary-color);
      display: flex;
      align-items: center;
      cursor: pointer;

      .anticon {
        font-size: 20px;
        margin-right: 6px;
      }

      span {
        text-decoration: underline;
      }
    }
  }

  .homework-table-style {
    width: 100%;
    height: calc(100% - 52px);
    display: flex;
    flex-direction: column;
    gap: 10px;

    .homework-item {
      display: flex;
      //flex-direction: column;
      width: 100%;
      border: 1px solid #e8e8e8;
      border-radius: 6px;
      padding: 5px 10px;
      justify-content: space-between;
      height: 80px;
      cursor: pointer;
      .top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .homework-title {
          color: #000;
          font-size: 14px;
          margin: 0 !important;
          width: 180px;
          overflow: hidden;
          text-overflow:ellipsis;
          white-space: nowrap;
        }
        &-right {
          width: 290px;
          padding-left: 8px;
          display: flex;
          justify-content: flex-end;
          &-content {
            display: flex;
            flex-wrap: nowrap;
            align-items: center;
            margin-bottom: 0;
          }

        }
      }
      .bottom {
        &-content {
          justify-content: space-between;
          align-items: center;
          flex-wrap: wrap;
          gap: 40px;
        }
      }
      .action-btn {
        width: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
        //gap: 15px;
        font-size: 22px;
        color: var(--primary-color);

        .submit-tip {
          color: #30AE3C;
          font-size: 14px;

          .anticon {
            visibility: visible;

          }
        }
        .anticon {
          visibility: hidden;

        }
      }

      &:hover {
        background-color: var(--primary-bg-color);

        .anticon {
          visibility: visible !important;
        }
      }
    }

    .draft {
      background: #31AC3C;
      padding: 4px 10px;
      border-radius: 3px;
      font-size: 14px;
      margin-right: 10px;
      color: #fff;
      font-family: '黑体';
    }

    .left {
      width: 40%;
      white-space: nowrap;
      display: flex;
      flex-direction: column;
      justify-content: space-around;


      .homework-title {
        color: #000;
        font-size: 16px;
      }

      .homework-title:hover, .team:hover, .team_name:hover {
        color: var(--primary-color);
        text-decoration: underline;
      }

      .edit-title:hover {
        color: var(--primary-color);
        text-decoration: underline;
      }
    }

    .right {
      display: flex;
      align-items: center;
      gap: 15px;
      color: #000;
    }

    .submit-info-notice:hover {
      color: var(--primary-color);
      text-decoration: underline;
    }
  }

  .ant-tree-treenode {
    font-family: PingFangSC-Regular, PingFang SC;
    height: 40px;
    display: flex;
    align-items: center;
    padding: 0 10px;

    &.section-item {
      height: 34px;
      font-size: 16px;
      // background: #F7F8FA;
    }

    &.chapter-item {
      font-size: 16px;
      // background-color: transparent;
    }

    .ant-tree-switcher {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .ant-tree-switcher-icon {
      font-size: 10px;
    }
  }

  .ant-tree .ant-tree-node-content-wrapper:hover {
    background: transparent;
  }

  .empty-header {
    height: 100%;
    font-size: 16px;
    // font-weight: bold;
  }

  .empty-container {
    height: 100%;
    position: relative;

    a {
      display: inline-block;
      margin: 3px;
    }
  }

  .empty-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    .anticon-loading {
      font-size: 30px;
      color: var(--primary-color);
    }
  }
}

.team-person-popverstyle {
  .ant-popover-title {
    color: rgba(0, 0, 0, 0.85);
    border-bottom: 1px solid #f0f0f0;
    background: #f6f6f6;
    font-family: '宋体';
    padding: 6px 16px;
    font-weight: bold;
  }

  .team-person-name {
    font-family: '宋体';
    display: flex;
    flex-direction: column;
    gap: 6px;
    padding-left: 10px;
  }
}

//@media screen and (max-width: 768px) {
//  .homework-container {
//    width: 100%;
//
//    .tree-container {
//      padding: 14px;
//
//      .ant-tree-title {
//        white-space: pre-wrap;
//      }
//
//      .ant-tree-treenode {
//        height: auto;
//        padding: 0;
//        padding-top: 6px;
//        padding-bottom: 6px;
//      }
//
//      .stuHomeworkItem-container {
//        flex-wrap: wrap;
//        padding-right: 0;
//
//        .left-container {
//          width: 100%;
//
//          .tag {
//            white-space: nowrap;
//          }
//
//          .title {
//            flex: 1;
//            width: 0;
//            white-space: nowrap;
//            overflow: hidden;
//            text-overflow: ellipsis;
//          }
//        }
//
//        .right-container {
//          flex-wrap: wrap;
//
//          &>div {
//            width: 100%;
//          }
//
//          .score-container {
//            margin-left: 0;
//          }
//        }
//      }
//    }
//  }
//  .homework-table-style {
//    .homework-item {
//      //.top {
//      //  flex-direction: column;
//      //}
//      .bottom {
//        &-content {
//          justify-content: space-between;
//          align-items: center;
//          flex-wrap: wrap;
//          gap: 0 !important;
//        }
//      }
//
//    }
//  }
//}
