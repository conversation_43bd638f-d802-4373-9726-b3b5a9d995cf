import React from 'react';
import './index.less';
import kecheng from '@/assets/imgs/HomeNav/kecheng.png';
import shiti from '@/assets/imgs/HomeNav/shiti.png';
import zhishiditu from '@/assets/imgs/HomeNav/zhishiditu.png';
import AIagent from '@/assets/imgs/HomeNav/AIAgent.png';
import weizhuanye from '@/assets/imgs/HomeNav/weizhuanye.png';
import ziyuan from '@/assets/imgs/HomeNav/ziyuan.png';
import jiaoshiliu from '@/assets/imgs/HomeNav/jiaoshiliu.png';
import renwujiankong from '@/assets/imgs/HomeNav/renwujiankong.png';
import ziyuanbao from '@/assets/imgs/HomeNav/ziyuanbao.png';
import gongjuxiang from '@/assets/imgs/HomeNav/gongjuxiang.png';
import shujutongji from '@/assets/imgs/HomeNav/shujutongji.png';
import jiashicang from '@/assets/imgs/HomeNav/jiashicang.png';
import jiaoxuedudao from '@/assets/imgs/HomeNav/jiaoxuedudao.png';
import peiyangfangan from '@/assets/imgs/HomeNav/peiyangfangan.png';
import xitongguanli from '@/assets/imgs/HomeNav/xitongguanli.png';


interface NavItemProps {
  icon: string;
  text: string;
  link: string;
}

const HomeNav: React.FC = () => {

  // 处理导航项点击
  const handleNavItemClick = (link: string) => {
    //打开新标签
    window.open(link, '_blank');
  };

  // 导航数据，根据图片中的内容构建
  const navData = [
    {
      category: '智教运行',
      items: [
        { icon: kecheng, text: '课程', link: '/learn/workbench/#/course' },
        { icon: zhishiditu, text: '知识地图', link: '/learn/workbench/#/coursemap/minemap' },
        { icon: shiti, text: '试题', link: '/exam/#/exam/topicManage' },
        { icon: AIagent, text: 'AI Agent', link: '/learn/workbench/#/agent' },
        { icon: weizhuanye, text: '微专业', link: '/learn/search/microMajor' },
      ]
    },
    {
      category: '资源智采',
      items: [
        { icon: ziyuan, text: '资源', link: '/rman/#/basic/rmanCenterList' },
        { icon: jiaoshiliu, text: '教室流监控', link: '/unifiedplatform/#/overview/home' },
        { icon: renwujiankong, text: '录播任务监控', link: '/unifiedplatform/#/cockpit/screen/record?hideLeft=1&hideHeader=1' },
      ]
    },
    {
      category: '数字出版',
      items: [
        { icon: ziyuanbao, text: '课程资源包', link: '/learn/workbench/#/coursetemplate/mytemplate' },
        { icon: gongjuxiang, text: '制作工具箱', link: '/aitools/#/overview' }
      ]
    },
    {
      category: '智教管服',
      items: [
        { icon: shujutongji, text: '数据统计', link: '/unifiedplatform/#/statistics/overview' },
        { icon: jiashicang, text: '驾驶舱', link: '/unifiedplatform/#/management' },
        { icon: jiaoxuedudao, text: '教学督导', link: '/supervision' },
        { icon: peiyangfangan, text: '培养方案', link: '/pyfa/#/train/scheme' },
        { icon: xitongguanli, text: '系统管理', link: '/unifiedplatform/#/' },
      ]
    },
  ];

  // 渲染导航项
  const renderNavItem = (item: any) => (
    <div
      className="home-nav-item"
      key={item.text}
      onClick={() => handleNavItemClick(item.link)}
    >
      <div className="home-nav-item-icon">
        <img src={item.icon} alt={item.text} />
      </div>
      <div className="home-nav-item-text">{item.text}</div>
    </div>
  );

  return (
    <div className="home-nav-container">
      {navData.map((section, index) => (
        <div className="home-nav-section" key={index}>
          <div className="home-nav-section-title">{section.category}</div>
          <div className="home-nav-section-content">
            {section.items.map(item => renderNavItem(item))}
          </div>
        </div>
      ))}
    </div>
  );
};

export default HomeNav;
