.home-nav-container {
  width: 100%;
  padding: 30px 100px 0px;
  overflow-y: hidden;

  .home-nav-section {
    width: 100%;
    height: 156px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-bottom: 60px;


    .home-nav-section-title {
      width: 100%;
      height: 40px;
      font-size: 16px;
      line-height: 35px;
      font-family: PingFang SC;
      padding-left: 32px;
      position: relative;
      color: #fff;
      border-bottom: 1px solid #DDDDDD;
      background-image: url('../../../../assets/imgs/HomeNav/category_bg.png');
      background-repeat: no-repeat;
      background-size: contain;

      // &::before {
      //   content: '';
      //   position: absolute;
      //   left: 24px;
      //   top: 50%;
      //   transform: translateY(-50%);
      //   width: 6px;
      //   height: 16px;
      //   background-color: #fff;
      // }
    }

    .home-nav-section-content {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
    }
  }
}

.home-nav-item {
  cursor: pointer;
  width: 260px;
  height: 80px;
  background: #FFFFFF;
  box-shadow: 0px 2px 12px 2px rgba(129, 129, 129, 0.1);
  border-radius: 8px;
  margin-right: 30px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  transition: all 0.15s ease;
  padding-left: 33px;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0px 4px 16px 4px rgba(129, 129, 129, 0.3);
  }

  .home-nav-item-icon {
    width: 55px;
    height: 55px;
  }

  .home-nav-item-text {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #2E2E2E;
    line-height: 22px;
    text-align: left;
    font-style: normal;
  }
}