import React, { useState, useEffect } from 'react';
import { Button, Form, Input, Modal, Select, Table, message } from 'antd';
import Style from './index.less';
import useLocale from '@/hooks/useLocale';
import { getSemesters } from '@/api/course';
import { IconFont } from '@/components/iconFont';
import { ReloadOutlined } from '@ant-design/icons';
import { getMyVideoCourse, getRoucosePoint } from '@/api/coursemap';
import { createNodeoredge, traversalTree } from '../../Editmap/util';
const { confirm } = Modal;

interface SelectRecordingProps {
  addtype:number;
  selectnode:any,
  graph:any,
  onOk: (newdata:any) => void;
  onCancel: () => void;
}

const SelectRecording: React.FC<SelectRecordingProps> = ({ onOk,addtype,selectnode,graph,onCancel }) => {
  // 加载状态
  const [loading, setLoading] = useState<boolean>(false);
  // confirmLoading
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  //国际化
  const { t } = useLocale();
  // 学期列表
  const [semesters, setSemesters] = useState<any[]>([]);
  // 表单
  const [form] = Form.useForm();
  // 表格数据源
  const [dataSource, setDataSource] = useState<any[]>([]);
  // 表格分页配置
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条`,
    onChange: (page: number, size: number) => {
      fetchRecordingList(page, size);
    }
  })

  // 选中的录播课程 key
  const [selectedRowKey, setSelectedRowKey] = useState<any>([]);

  // 表格列配置
  const columns:any = [
    {
      title: t('课程名称'),
      dataIndex: 'course_name',
      key: 'course_name',
      align: 'center', // 表头居中
      render: (text: string) => (
        <span style={{ fontWeight: 'bold' }}>{text}</span>
      )
    },
    {
      title: t('资源数'),
      dataIndex: 'videoCount',
      key: 'videoCount',
      align: 'center', // 表头居中
      render: (text: number) => (
        <span>{text || 0}</span>
      )
    },
    {
      title: t('教师'),
      dataIndex: 'teacher_name',
      key: 'teacher_name',
      align: 'center' // 表头居中
    },
    {
      title: t('教室'),
      dataIndex: 'area_name',
      key: 'area_name',
      align: 'center' // 表头居中
    },
    {
      title: t('学期'),
      dataIndex: 'semester',
      key: 'semester',
      align: 'center' // 表头居中
    }
  ]


  // 初始化获取录制列表
  useEffect(() => {
    fetchRecordingList();
  }, []);

  // 获取录制列表数据
  const fetchRecordingList = async (page = 1, size = 10) => {
    // 获取表单当前值
    const values = form.getFieldsValue();
    // 构建接口参数
    const params = {
      page,
      size,
      courseName: values.keyword || '',
      semester: values.semester || ''
    };
    setLoading(true); // 设置加载状态
    getMyVideoCourse(params).then((res:any) => {
      setLoading(false); // 关闭加载状态
      if(res && res.data.success){
        // 设置表格数据源
        setDataSource(res.data.data.results || []);
      } else {
        setDataSource([]);
        message.error(res.data?.message || '获取录制列表失败');
      }
    }).catch(() => {
      setLoading(false);
      setDataSource([]);
      message.error('获取录制列表失败');
    });
  };

  // 选择录制
  const handleOk = () => {
    // 如果没有选中任何课程，提示用户
    if (selectedRowKey === undefined) {
      message.warning('请先选择一个录播课程！');
      return;
    }
    setConfirmLoading(true);
    // 选中的课程信息
    const selectedCourse = dataSource.find(item => item.course_id === selectedRowKey);
    if (selectedCourse) {
    //   onOk(selectedCourse);
    getRoucosePoint({
        "courseId": selectedCourse.course_id,
        "courseNo": selectedCourse.course_no,
        "courseName": selectedCourse.course_name,
        "semester": selectedCourse.semester,
        "week": null,
        "videoIds": []
    }).then((res:any) => {
        if(res && res.data.success){ 
            if(addtype  === 0){
                const rootnodes = graph.getRootNodes();
                if(rootnodes.length === 0){
                  message.error('请先创建根节点');
                  return;
                }
                const newdata = traversalTree(res.data.data.videoKnowledgeGraphs, rootnodes[0]);        
                onOk(newdata);
                setConfirmLoading(false);
              }else{
                if(!selectnode){
                  message.error('请选中一个节点');
                  return;
                }
                const newdata = createNodeoredge(res.data.data.videoKnowledgeGraphs, selectnode);        
                onOk(newdata);   
                setConfirmLoading(false);       
              }                 
        }else{
          message.error(res.data.error.title);
        }
        setConfirmLoading(false);
      })

    } else {
      message.warning('获取选中课程信息失败，请重试！');
    }
  }

  // 取消选择
  const handleCancel = () => {
    // 清空选中状态
    setSelectedRowKey([]);
    // 调用父组件的取消回调
    onCancel();
  }

  const querySemester = () => {
    getSemesters().then((res: any) => {
      if (res.status === 200) {    
        setSemesters(res.data.semesterDataList);
        form.setFieldValue('semester',res.data.currentSemester?.showSemester || res.data.currentSemester?.name || '');
      } else {
        message.error(res.message);
      }
    });
  };

  const reset = () => {
    form.resetFields();
    fetchRecordingList(1, pagination.pageSize);
  }

  const search = () => {
    // 搜索时重置为第一页
    fetchRecordingList(1, pagination.pageSize);
  }

  
// 单选框配置
const rowSelection = {
	type: 'radio' as const,
	selectedRowKeys: selectedRowKey !== undefined ? [selectedRowKey] : [],
	onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
		// 只允许单选，selectedRowKeys 数组长度为 1
		setSelectedRowKey(selectedRowKeys[0]);
	}
};
  
useEffect(() => {
	querySemester();
  }, []);

// 确认提示框
const handleConfirm = () => {
    confirm({
      title: t(`导入数据会覆盖当前编辑内容，是否确定？`),
      onOk() {
        handleOk();
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  }

  return (
    <Modal title="我的录播" width={1100} open={true} onOk={()=>{
        if(addtype === 0){
            handleConfirm();
          }else{
            handleOk();
          }
    }} onCancel={handleCancel} getContainer={false} confirmLoading={confirmLoading}>
        <div className={Style.search}>
            <Form layout="inline" name="basic" form={form}>
                <Form.Item name="keyword">
                    <Input placeholder="输入关键词" style={{width:'250px'}} allowClear />
                </Form.Item>
                <Form.Item name="semester">
                    <Select style={{ width: 220 }}
                        placeholder={t('请选择所属学期')}
                        allowClear
                    >
                        <Select.Option key={Date.now()} value={''}>
                        {t('全部学期')}
                        </Select.Option>
                        {semesters?.map((item: any) => (
                        <Select.Option key={item.id} value={item.id}>
                            {item.showSemester || item.name}
                        </Select.Option>
                        ))}
                    </Select>
                </Form.Item>
                <div className={Style['reset-wrp']} onClick={reset}>
                  <span style={{marginRight:'5px'}}>{t('清空')}</span>
                  <ReloadOutlined />
                </div>
                <Button type="primary" onClick={search}>
                  {t('搜索')}
                  <IconFont type="iconsousuo2" />
                </Button>
            </Form>
        </div>
        <Table dataSource={dataSource} pagination={pagination} columns={columns} loading={loading} rowSelection={rowSelection} rowKey={record => record.course_id} />
    </Modal>
  );
};

export default SelectRecording;


