.Custom_modal {
  .ant-modal-header {
    .tp-select-header {
      color: #999;
      display: flex;
      align-items: center;

      .ant-input {
        margin: 0 5px;
        height: 30px;
        border-radius: 0;
        width: 80px;
      }
    }
  }

  .ant-modal-content {
    .ant-modal-body {
      padding: 0;

      .Custom-box {
        .btn-box {
          max-height: 403px;
          min-height: 400px;
          display: flex;

          .box-left {
            width: 30%;
            border-right: 2px solid #eee;

            .left-list {
              max-height: 350px;
              overflow: auto;

              .group-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10px;
                margin: 6px 0;
                border-radius: 4px;
                transition: all 0.3s;

                &.active {
                  background: #f5f5f5 !important;
                }
              }

              .delete-icon {
                color: #000000;
                padding: 4px;
                transition: all 0.3s;
              }
            }
          }

          .box-right {
            width: 70%;

            .right-list {

              .right-header {
                display: flex;
                justify-content: flex-end;
                align-items: center;
              }

              .right-item {
                padding: 10px 20px;
                background-color: #f6f6f6;

                .itemright {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;

                }
              }

              .itmebox {
                max-height: 300px;
                overflow: auto;
              }

              .itmeList {
                padding: 10px 20px;
                // display: flex;
                // justify-content: space-between;
                // align-items: center;
                color: var(--primary-color)
              }

              .itemrow {
                display: flex;
                justify-content: space-between;
                align-items: center;
              }
            }
          }
        }
      }
    }

    .group-itemList {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      margin: 6px 0;
      border-radius: 4px;
      transition: all 0.3s;
      cursor: pointer;

      &.active {
        background: #f5f5f5 !important;
      }
    }
  }

  .ant-table-pagination.ant-pagination {
    margin: 16px 10px;
  }
}
