import { CloseOutlined } from '@ant-design/icons';
import React from 'react';
import { Spin } from 'antd';

interface dataModal {
    open: boolean,
    loading?: boolean;
    data?: any
}
/**
 * 
 * 注意：此次页面为演示用，实际使用时请根据实际情况进行修改
 * 包括：
 * knowledgePointModal.data为假数据
 * indow.gptInfo.currentGpt.id为假数据，需改为 该课程绑定的agent 信息
 *

 */

const VideoPauseDemo = (
    { knowledgePointModal, centerednode, canclePauseCard }:
        {
            canclePauseCard: () => void;
            knowledgePointModal: dataModal;
            centerednode?: (id: string) => void;
        }) => {

    const { open: modalOpen, data: modalData, loading: modalLoading } = knowledgePointModal;


    // 打开AI工具面板小卡片
    const openAitools = () => {
        window.gptInfo.currentGpt = { id: '3dc4dd12-fd97-11ef-a805-86db9ed4fc23', initQuestion: modalData?.selectNodeName }; // 假数据
        const aiIcon = document.getElementById('chat_gpt_btn');
        aiIcon && aiIcon.click();
        // console.info(window, window?.gptInfo)
    }



    return (
        modalOpen ? <div className="knowledge_point_video_card">
            <div className='header'>
                <p>知识点推荐</p>
                <p onClick={canclePauseCard}><CloseOutlined /></p>
            </div>
            <Spin spinning={modalLoading}>
                <div className="content">
                    {Array.isArray(modalData?.knowLedgePoints) && modalData.knowLedgePoints.map((item: any) => (
                        <div key={item.id} className="item" onClick={() => centerednode && centerednode(item.id)}>{item.name}</div>
                    ))}
                </div>
            </Spin>
            <div className="fooder">学习困难？<span className="open_aitools" onClick={openAitools}>问问助手</span></div>
        </div> : null
    )
}


export default VideoPauseDemo;