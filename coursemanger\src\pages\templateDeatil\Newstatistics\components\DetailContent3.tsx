import React, { FC, useEffect, useState } from 'react';
import { useHistory, useLocation } from 'umi';
import './DetailContent2.less';
import { Input, Select, Table, Button, Space, Tooltip, Tag } from 'antd';
import xlsx from '@/components/entity/excel';
import { LeftOutlined,ExclamationCircleOutlined,SearchOutlined } from '@ant-design/icons';
import ResourcePreviewModal from '@/components/ResourcePreviewModal';
import statisticsApi from '@/api/statistics';
import baseInfoApi from '@/api/baseInfo';
import { l100Ns2Hms } from '@/utils';
import useLocale from '@/hooks/useLocale';
import { useIsSJ } from '../hook';
import { Utils } from '@/utils/utils';

interface IDetailContent {
  onBack: () => void;
  detail: any;
}

const DetailContent: FC<IDetailContent> = ({ onBack, detail }) => {

  const { t } = useLocale();
  const location: any = useLocation();
  let history: any = useHistory();
  const { id,sm } = location.query;
  const [dataSource, setDataSource] = useState<object[]>([]);
  const [keyword, setKeyword] = useState('');
  const [isDesc,setIsDesc] = useState('')
  const [sortField,setSortField] = useState('')
  const [loading, setLoading] = useState<boolean>(false);
  const [pagination, setPagination] = useState<any>({
    current: 1,
    position: ["bottomCenter"],
    pageSize: 10,
    total: 0,
    // size: "small",
    showTotal: (total: number) => t("共{name}条", String(total)),
    showQuickJumper: true,
    showSizeChanger: true
  });
  const isSJ = useIsSJ()

  const getPeopleList = (toPage1?: boolean) => {
    setLoading(true);
    if (toPage1) {
      setPagination({
        ...pagination,
        current: 1
      });
    }
    statisticsApi.getStudentLearning({
      courseId: id,
      courseSemester: sm,
      page: toPage1 ? 1 : pagination.current,
      size: pagination.pageSize,
      keyword: keyword,
      nodeId: detail.nodeId,
      sortField,
      isDesc

    }).then((res) => {
      console.log(res);

      if (res.data.status == 200) {
        setDataSource(res.data.data.results);
        setPagination({
          ...pagination,
          current: res.data.data.page,
          size: res.data.data.size,
          total: res.data.data.total
        });
      }
    }).finally(() => {
      setLoading(false);
    });
  };
  useEffect(() => {
    getPeopleList();
  }, [pagination.current, pagination.pageSize,isDesc,sortField]);
  const tablechange = (pagination: any,filter:any ,sorter: any) => {
    console.log(sorter)
    let isDesc: any = sorter.order ?
      sorter.order == 'descend' ?
        true :
        false :
      null;
    setIsDesc(isDesc)
    let sortField: any = isDesc != null ? sorter.field : null;
    setSortField(sortField)
    setPagination({
      ...pagination,
      current: pagination.current,
      pageSize: pagination.pageSize
    });

  };

  const defaultColumns: any = Utils.strongColumns([
    {
      title: t("姓名"),
      dataIndex: 'userName',
      key: 'userName',
      align: 'center'
    },
    {
      title: t("学工号"),
      dataIndex: 'userCode',
      key: 'userCode',
      align: 'center',
      hideInTable: isSJ
    },

    {
      title: t("学院"),
      dataIndex: 'college',
      key: 'college',
      align: 'center',
      hideInTable: isSJ

    },
    {
      title: t("专业"),
      dataIndex: 'major',
      key: 'major',
      align: 'center',
      hideInTable: isSJ

    },
    {
      title: t("知识点完成率"),
      dataIndex: 'finishRate',
      key: 'finishRate',
      align: 'center',
      sorter: true,
      render: (text: any, record: any) => {
        return <span>{text}%</span>;
      }
    },
    {
      title: t("知识点掌握率"),
      dataIndex: 'masterRate',
      key: 'masterRate',
      align: 'center',
      sorter: true,
      render: (text: any, record: any) => {
        return <span>{text}%</span>;
      }
    }]);



  return (
    <div className='resource_detail_content'>
      <div className="header">
        <Button type="text" icon={<LeftOutlined />} onClick={() => onBack()}>{t("返回")}</Button>
      </div>
      <div className="content">
        <div className="heards_view">
          <span className="name">{detail.nodeName}</span>
          <div className="right_view">
            <span>{t("完成率：")}{Number(detail.finishRate) || 0}%</span>
            <span style={{ marginLeft: '30px' }}>{t("掌握率：")}{Number(detail.masterRate) || 0}%</span>
            <span
              style={{ marginLeft: '30px', color: '#878B90', fontSize: '14px' }}>
            {t("（旁听学生未计入统计）")}

          </span>
          </div>
        </div>
        <div className="table">
          <div className="table_title">{t("学员学习情况")}</div>
          <div className='filter' style={{marginBottom:20}}>
            <Space>
              <Input placeholder="请输入学员姓名/学工号" allowClear onChange={(e: any) => setKeyword(e.target.value)} onPressEnter={() => getPeopleList(true)} />
              <Button type="primary" icon={<SearchOutlined />} onClick={() => getPeopleList(true)}>
                搜索
              </Button>
            </Space>
          </div>
          <Table size='small' dataSource={dataSource} loading={loading} rowKey={(record: any) => record.code} columns={defaultColumns} pagination={{ ...pagination, size: "small" }} showSorterTooltip={false} onChange={tablechange} />
        </div>
      </div>

    </div>);

};

export default DetailContent;
