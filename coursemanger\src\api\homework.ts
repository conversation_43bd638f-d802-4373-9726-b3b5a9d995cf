import HTTP from './index';
import { message } from 'antd';

// 作业列表
export function searchHomeworkList(courseId: string) {
  return HTTP.get(`/exam-api/resource/homework/course/homework/list?courseId=${courseId}`).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  })
  .catch(error => {
    console.error(error);
  });
}


//  查询组内人员名单
export function searchTeamPersonList(params: any) {

  return HTTP.get(`/exam-api/resource/homework/course/homework/team/student/list`, { params}).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  })
  .catch(error => {
    console.error(error);
  });
}

// 删除作业(作业页面)
export function deleteHomeworkApi(data: any) {
  return HTTP.post(`/exam-api/resource/homework/course/delete`, data)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// * 课程库

export function getHomeworkMapInfo(courseId: string) {
  return HTTP.get(`/exam-api/resource/homework/map-info?courseId=${courseId}`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function getHomeworkSubmitInfo(courseId: string) {
  return HTTP.get(`/exam-api/submit/resource/submit-info?courseId=${courseId}`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function createHomework(pageType: 'resource' | 'template', data: any) {
  return HTTP.post(`/exam-api/${pageType}/homework/pre`, data)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function updateHomework(
  pageType: 'resource' | 'template',
  resourceHomework: any,
) {
  return HTTP.post(`/exam-api/${pageType}/homework/update`, resourceHomework)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function fetchTopicList(data: any) {
  return HTTP(`/exam-api/examination/list`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
//试题一键绑定
export function bindTopicList(data: any) {
  return HTTP(`/exam-api/examination/point/list`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function getHomeworkDetail(
  pageType: 'resource' | 'template',
  homeworkId: string,
  parentId: string | undefined,
  courseId: string,
) {
  return HTTP.get(
    `/exam-api/${pageType}/homework/detail?id=${homeworkId}&courseId=${courseId}${parentId ? `&parentId=${parentId}` : ''
    }`,
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function addHomeworkTopic(pageType: 'resource' | 'template', data: any) {
  return HTTP(`/exam-api/${pageType}/homework/question/create/batch`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function updateTopicOnPub(data: any) {
  return HTTP(`/exam-api/resource/homework/v2/question/create/batch`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function updateTopic(
  pageType: 'resource' | 'template',
  id: string,
  data: { sourceId: string; modifyTheMode: boolean; },
) {
  return HTTP(`/exam-api/${pageType}/homework/question/${id}/update`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function sortTopic(pageType: 'resource' | 'template', data: any) {
  return HTTP(`/exam-api/${pageType}/homework/question/sort`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function getPapers(data: any) {
  return HTTP(`/exam-api/paper/list`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function updateScore(pageType: 'resource' | 'template', data: any) {
  return HTTP(`/exam-api/${pageType}/homework/question/score/batch`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function deleteTopic(pageType: 'resource' | 'template', id: string) {
  return HTTP(`/exam-api/${pageType}/homework/question/${id}/delete`, {
    method: 'POST',
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
export function batchDeleteTopic(
  pageType: 'resource' | 'template',
  ids: string[],
) {
  return HTTP.post(`/exam-api/${pageType}/homework/question/batch/delete`, ids)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 个人提交列表
export function getSubmissions(params: any) {
  return HTTP(`/exam-api/resource/homework/records`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 小组提交列表
export function getTeamSubmissions(params: any) {
  return HTTP(`/exam-api/resource/homework/team/records`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function getSubmissionDetail(id: string) {
  return HTTP.get(`/exam-api/resource/homework/record/${id}`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
export function getSubmissionDetail2(id: string, params: any) {
  return HTTP.get(`/exam-api/resource/homework/record/${id}`, {
    params
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function submitScore(id: string, data: any) {
  return HTTP(`/exam-api/resource/homework/record/${id}/check`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
export function submitScoreCorrection(id: string, data: any, params: any) {
  return HTTP(`/exam-api/resource/homework/record/${id}/check`, {
    method: 'POST',
    data: JSON.stringify(data),
    params: params
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function getTemplateHomeworkList(params: any) {
  return HTTP(`/exam-api/template/homework/list`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function getStuHomeworkDetail(
  homeworkId: string,
  parentId: string,
  courseId: string,
) {
  return HTTP.get(
    `/exam-api/submit/resource/homework?homeworkId=${homeworkId}&courseId=${courseId}${parentId ? `&parentId=${parentId}` : ''
    }`,
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function getStuSubRecord(
  homeworkId: string,
  courseId: string,
  stuCode?: string,
) {
  return HTTP.get(
    `/exam-api/submit/resource/record?homeworkId=${homeworkId}&courseId=${courseId}${stuCode ? `&stuCode=${stuCode}` : ''
    }`,
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function uploadFile(data: any) {
  return HTTP.post(`/rman/v1/upload/save/file`, data, {
    headers: { 'Content-Type': 'multipart/form-data' },
  })
    .then(res => {
      if (res?.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}
export function deleteFile(data: any, isPublic: boolean = true) {
  return HTTP.post(`/rman/v1/recycle/delete`, data, { params: { isPublic } })
    .then(res => {
      if (res?.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

export function submitRecord(data: any) {
  return HTTP(`/exam-api/submit/resource`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function submitMicroMajorHomework(courseSemester: any, data: any) {
  return HTTP(`/exam-api/micro/submit/resource/create?courseSemester=${courseSemester}`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function studentGetMicroMajorHomework(courseId: any, courseSemester: any) {
  return HTTP(`/exam-api/submit/resource/submit-info?courseId=${courseId}&courseSemester=${courseSemester}`).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  }).catch(error => {
    console.error(error);
  });
}

export function helpSubmitRecord(data: any) {
  return HTTP(`/exam-api/resource/homework/help-submit`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function copyHomework(data: any) {
  return HTTP(`/exam-api/resource/homework/copy`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function deleteHomework(data: any) {
  return HTTP(`/exam-api/resource/homework/delete`, {
    method: 'POST',
    params: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function deleteTempHomework(data: any) {
  return HTTP(`/exam-api/template/homework/delete`, {
    method: 'POST',
    params: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function getLearnStatus(data: any) {
  return HTTP('/learn/v1/learningsituation', {
    method: 'GET',
    params: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function updateLearnStatus(data: any) {
  return HTTP(`/learn/v1/learningsituation`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function updateQuote(data: any) {
  return HTTP(`/exam-api/template/homework/quote`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function deleteHomeworks(data: string[]) {
  return HTTP(`/exam-api/resource/homework/batch/delete`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function deleteTempHomeworks(data: string[]) {
  return HTTP(`/exam-api/template/homework/batch/delete`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function deleteHomeworkFile(data: any) {
  return HTTP(`/exam-api/submit/resource/delete/attachment`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function downloadHomeworkFilesCheck(params: any) {
  return HTTP(`/exam-api/resource/homework/download/batch/check`, {
    method: 'GET',
    params,
    // timeout: 3600000,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
export function downloadHomeworkFiles(params: any) {
  return HTTP(`/exam-api/resource/homework/download/batch`, {
    method: 'GET',
    params,
    // timeout: 3600000,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
export function getDownLoadFiles(params: any) {
  return HTTP(`/exam-api/resource/homework/download/batch/schedule`, {
    method: 'GET',
    params,
    // timeout: 3600000,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}


export function getDownLoadbatchFiles(params: any) {
  return HTTP(`/exam-api/resource/homework/download/word/batch`, {
    method: 'GET',
    params,
    // timeout: 3600000,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function getDownLoadbatchFilesbatch(params: any) { //附件作业
  return HTTP(`/exam-api/resource/homework/download/batchWordAndAttach`, {
    method: 'GET',
    params,
    // timeout: 3600000,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function downloadSubmissions(params: any) {
  return HTTP(`/exam-api/resource/homework/download`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function dragHomework(data: any) {
  return HTTP(`/exam-api/resource/homework/drag/batch`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function dragHomeworkTemp(data: any) {
  return HTTP(`/exam-api/template/homework/drag/batch`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function queryNoCheckHomework(params: any) {
  return HTTP(`/exam-api/resource/homework/nocheck/num`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function queryHomeworkFallback(params: any) {
  return HTTP(`/exam-api/resource/homework/fallback`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
export function createHomeworkFallback(data: any) {
  return HTTP(`/exam-api/resource/homework/fallback`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function updateHomeworkFallback(data: any) {
  return HTTP(`/exam-api/resource/homework/fallback/update`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function updateReadFallback(data: any) {
  return HTTP(`/exam-api/resource/homework/fallback/status`, {
    method: 'POST',
    params: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function importScores(data: any, params: any) {
  return HTTP(`/exam-api/resource/homework/batch-score`, {
    method: 'POST',
    data,
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
export function exportScoresTpl(params: any) {
  return HTTP(`/exam-api/resource/homework/batch-score/template`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function getTopicByIds(data: any) {
  return HTTP(`/exam-api/examination/getQuestionsByIds`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function urgeHomework(id: string, data: any) {
  return HTTP(`/exam-api/resource/homework/urging/${id}`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function checkIsSubmitTopic(id: string) {
  return HTTP(`/exam-api/submit/resource/check/update/submit`, {
    method: 'POST',
    params: { id },
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

//自动生成试题
export function assistantAiGEQ(data: any) {
  return fetch('/terminator/api/v1/skillSet/AiGEQ', {
    method: 'POST',
    body: JSON.stringify(data), // 确保数据是正确格式化的
    headers: {
      'Content-Type': 'application/json',
      'Accept': '*/*',
    },
  })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      // 返回一个ReadableStream
      return response.body;
    })
    .then(readableStream => {
      // 使用流API处理数据
      console.log(readableStream);

      debugger;
      return new Response(readableStream).json(); // 注意：这不会流式处理JSON，只是简单地将流转换为Promise<JSON>
      // 或者，你可以使用流API来手动读取和处理数据块
    });
}
// 分片上传文件
export function uploadFileChunk(data: {
  chunk: string;  // 块序号0开始 
  guid: string;  // 文件分块上传唯一id
  file: File;  // 文件块
  classification: string; // 用于分类文件夹名默认other；根据使用用途传值
  type: string; // 用于文件类型文件夹名默认other；根据文件类型传值
  originalName: string; // 原始文件名，需要带文件扩展名
}) {
  const formData = new FormData();
  formData.append('chunk', data.chunk);
  formData.append('guid', data.guid);
  formData.append('file', data.file);
  formData.append('classification', data.classification);
  formData.append('type', data.type);
  formData.append('originalName', data.originalName);

  return HTTP('/rman/v1/unified/upload/filechunk', {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// 文件合并
export function mergeFile(data: {
  fileName: string;
  guid: string;
  finalFilePath?: string;
  classification?: string;
  type?: string;
  keepName?: boolean;
  filePathType?: string;
}) {
  return HTTP('/rman/v1/unified/upload/filemerge', {
    method: 'POST',
    data,
  });
}

// 获取合并任务详情
export function getMergeProgress(fileGuid: string) {
  return HTTP(`/rman/v1/unified/upload/filemerge/progress/${fileGuid}`, {
    method: 'GET',
  });
}


export function mergeFilesharding(data: {
  homeworkId: string;
  parentId: string;
  index: number | null;
  questionId: string;
  courseId: string;
  stuCode: string;
  fileName: string;
  fileUrl: string;
}) {
  const formData = new FormData();
  formData.append('homeworkId', data.homeworkId);
  formData.append('parentId', data.parentId);
  formData.append('index', String(data.index));
  formData.append('questionId', data.questionId);
  formData.append('courseId', data.courseId);
  formData.append('stuCode', data.stuCode);
  formData.append('fileName', data.fileName);
  formData.append('fileUrl', data.fileUrl);

  return HTTP('/exam-api/submit/resource/attach/upload/sharding', {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}