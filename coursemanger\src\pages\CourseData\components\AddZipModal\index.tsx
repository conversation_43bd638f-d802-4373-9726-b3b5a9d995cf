import { Button, Checkbox, Modal, Progress, Spin,message } from 'antd';
import <PERSON><PERSON> from 'antd/lib/upload/Dragger';
import React, { useState } from "react";
import { useSelector } from "umi";
import Icon from "@ant-design/icons";
import { ReactComponent as plus_icon } from "@/assets/imgs/icon/plus_icon.svg";
import useLocale from "@/hooks/useLocale";
import { storageConfig } from '@/api/addCourse'
import { HandleZip,handleZipTask,folderImport,getFolderImport } from '@/api/course'
const PlusIcon = (props: any) => <Icon component={plus_icon} {...props} />;
const AddZipModal = ({visible,onClose,contentConfirmLoading,uploadOk,pathVal}: any) => {
  const { t } = useLocale();
  const [modalResourceLoading, setModalResourceLoading] = useState<boolean>(false);
  const [file, setFile] = useState<any>({});
  const [uploadStatus, setUploadStatus] = useState<"success" | "exception" | "active" | 'normal' | "">("");
  const [percent,setPercent] = useState(0);
  const [percent2,setPercent2] = useState(0);
  // 检查是否为zip文件的函数
  const isZipFile = (file: any) => {
    // 检查MIME类型
    const validMimeTypes = [
      'application/zip',
      'application/x-zip-compressed',
      'application/x-zip',
      'application/octet-stream'
    ];

    // 检查文件扩展名
    const fileName = file.name.toLowerCase();
    const hasZipExtension = fileName.endsWith('.zip');

    // 同时检查MIME类型和文件扩展名
    const hasMimeType = validMimeTypes.includes(file.type);

    return hasZipExtension && (hasMimeType || file.type === '');
  };

  const props = {
    name: 'file',
    multiple: false,
    showUploadList: false,
    accept: '.zip',
    customRequest: () => true,
    onChange: async (info: any) => {
      if (!isZipFile(info.file)) {
        message.error('只能上传 zip 格式的压缩文件！');
        return;
      }
      console.log(info,'888')
      setFile(info.file);
      setUploadStatus("normal")
      const path = await getPath([{
        fileName: info.file.name,
        fileLength: info.file.size,
        fileType: 'other',
        poolType:window.localStorage.getItem('upform_platform')==='Lark'?'ROLE':'',
        pathType:1
      }])

      console.log('path',path)
      if (path) {
        setPercent(50)
        // path
        // info.file.size
       const data: string =  await HandleZipFn({ filePath: path, fileSize: info.file.size }) as string;
        console.log(data,'data333')
        if (!data) return;
        // setInterval( async () => {
          const bal:any = await pollTaskStatus(data)
        if (bal) {
          console.log(bal,'bal')
          setUploadStatus('active')
          console.log(pathVal,'pathVal')
        const id   =   await folderImportFn({
            folderPath: pathVal[0].path,
            zipFilePath: bal?.path || '',
            zipExtractPath: bal?.path.split('.')[0]  })
          if (id) {
            const val:any = await pollFolderStatus(id as string)
            if (val?.isCompleted) {
              message.success('上传成功')
              uploadOk()
            }
            console.log(val,'555')
          }
        }

        console.log(data,'3333')

          console.log('bal',bal)
        // },500)

      }
      // handlePercent()
      const { status } = info.file;
      // if (status === 'done') {
        // const path = info.file.response?.path;
        // console.log(path)
        // if (path) {
          // setUploadedPaths(prev => [...prev, path]);
        // }
        // message.success(`${info.file.name} 上传成功`);
      // }
      // console.log(info);
      // uploadFile(info.fileList)
      // console.log(info);
    },
    beforeUpload: (file) => {
      // 在上传前再次验证文件类型
      if (!isZipFile(file)) {
        message.error('只能上传 zip 格式的压缩文件！');
        return false;
      }

      // 检查文件大小限制（可选，比如限制为500MB）
      const maxSize = 500 * 1024 * 1024; // 500MB
      if (file.size > maxSize) {
        message.error('文件大小不能超过 500MB！');
        return false;
      }

      return true;
    },
    className: 'dragger',
    // ...uploadProps,
  };
  function getPath(params:any) {
    return new Promise((resolve, reject) => {
      storageConfig(params).then(res => {
        console.log(res)
        if (res.success) {
          resolve(res.data[0].path)
        }
      }).catch(err => {
        reject(null);
      })
    })
  }
  let intervalId: any;
   function  handlePercent() {
    if (!intervalId) {
      intervalId = setInterval(() => {
        // 使用函数式更新，获取最新值
        setPercent((prev) => {
          const newValue = prev + 10;
          if (newValue >= 100) {
            clearInterval(intervalId); // 达到 100 时清除定时器
            intervalId = null;
            return 100; // 直接设为 100，避免溢出
          }
          return newValue;
        });
      }, 100);
    }
  }
  function handlePercent2(num: number) {
    if (!intervalId) {
      intervalId = setInterval(() => {
        // 使用函数式更新，获取最新值
        setPercent2((prev) => {
          const newValue = prev + 10;
          if (newValue >= num) {
            clearInterval(intervalId); // 达到 100 时清除定时器
            intervalId = null;
            return num; // 直接设为 100，避免溢出
          }
          return newValue;
        });
      }, 100);
    }
  }
   function HandleZipFn(data:any) {
     return new Promise((resolve,reject) => {
       HandleZip(data).then(res => {
         console.log(res,'res')
         if (res.status === 200) {
           resolve(res.data.data);
         }
       }).catch(err => {
         reject(false)
       })
     })
  }
  function handleZipTaskFn(data: any) {
    let attempts = 0;
     return new Promise((resolve,reject) => {
       handleZipTask(data).then(res => {
         console.log(res,'res')
         if (res.status === 200) {
           resolve(res.data.data);
         }
       }).catch(err => {
         reject(err)
       })
     })
  }
  function pollTaskStatus(data: string) {
    let attempts = 0;
    let timeoutId: NodeJS.Timeout | null = null;

    return new Promise((resolve, reject) => {
      const checkStatus = async () => {
        try {
          const response = await handleZipTaskFn(data);
          console.log(attempts,'attempts')
          console.log('response', response);
          // 终止条件：达到最大尝试次数
          if (attempts >= 20) {
            clearTimeout(timeoutId!);
            return;
          }

          // 处理响应结果
          if (response != null) {
            clearTimeout(timeoutId!);
            setPercent(100)
            resolve(response);
          } else {
            attempts++;
            timeoutId = setTimeout(checkStatus, 300); // 继续轮询
            setPercent((prev) => {
              const newValue = prev + 10;
              if (newValue >= 90) {
                intervalId = null;
                return 90; // 直接设为 90
              }
              return newValue;
            });
          }
        } catch (error) {
         console.log('轮询失败:', error);
          clearTimeout(timeoutId!);
          // reject(error); // 显式拒绝 Promise
        }
      };

      checkStatus();
    });
  }
  function pollFolderStatus(data: string) {
    let attempts = 0;
    let timeoutId: NodeJS.Timeout | null = null;

    return new Promise((resolve, reject) => {
      const checkStatus = async () => {
        try {
          const response: any = await getFolderImportFn(data);
          console.log(attempts,'attempts')
          console.log('response', response);
          // 终止条件：达到最大尝试次数
          if (attempts >= 20) {
            clearTimeout(timeoutId!);
            return;
          }
          // 处理响应结果
          if (response != null && response?.isCompleted) {
            clearTimeout(timeoutId!);
            setPercent2(100)
            resolve(response);
          } else {
            attempts++;
            timeoutId = setTimeout(checkStatus, 500); // 继续轮询
            setPercent2((prev) => {
              const newValue = prev + 10;
              if (newValue >= 90) {
                intervalId = null;
                return 90; // 直接设为 90
              }
              return newValue;
            });
          }
        } catch (error) {
          console.log('轮询失败:', error);
          clearTimeout(timeoutId!);
          // reject(error); // 显式拒绝 Promise
        }
      };

      checkStatus();
    });
  }
  function folderImportFn(data: any) {
     return new Promise((resolve,reject) => {
       folderImport(data).then(res => {
         console.log(res)
         if (res.status === 200 && res.statusText === 'OK') {
           resolve(res.data.data);
         }
       }).catch(err => {
         reject(err)
       })
     })
  }
  function getFolderImportFn(data: any) {
     return new Promise((resolve,reject) => {
       getFolderImport(data).then(res => {
         console.log(res,'res')
         if (res.status === 200 && res.statusText === 'OK') {
           resolve(res.data.data);
         }
       }).catch(err => {
         reject(err)
       })
     })
  }
  const uploadFile = async (fileList: any[]) => {
    console.log(fileList);
    if (!fileList || fileList.length === 0) return;
    const fileObj = fileList[0].originFileObj;
    setFile(fileObj);
    // HandleZip({  }).then(res => {
    //压缩zip
    const data = await HandleZipFn({ filePath: '',fileSize: fileObj.size })
    setUploadStatus("normal");
    handlePercent()
    // const data2  = await handleZipTask(data)
    setTimeout(() => {
      setUploadStatus("active");
      handlePercent2()
    },5000)

    console.log(data)
    // setTimeout(() => {
    //   handlePercent('active')
    // },2000)
    // })
    // setModalResourceLoading(true);

    // 假设你有一个上传接口 uploadZipFile
    // try {
    //   // 这里用 fetch/axios/umi-request 均可，以下为伪代码
    //   const formData = new FormData();
    //   formData.append('file', fileObj);
    //
    //   // 伪代码：你需要根据实际接口替换
    //   // const response = await uploadZipFile(formData, {
    //   //   onUploadProgress: (progressEvent: any) => {
    //   //     // 计算进度百分比
    //   //     const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
    //   //     // 这里假设 setTaskPanls 是你用来更新进度的
    //   //     // setTaskPanls([{ progress: percent / 100 }]);
    //   //   }
    //   // });
    //
    //   setUploadStatus("success");
    //   // 上传成功后的处理
    //   // 你可以在这里调用 onClose 或其他回调
    // } catch (error) {
    //   setUploadStatus("exception");
    //   // 错误处理
    // } finally {
    //   setModalResourceLoading(false);
    // }
  };
  return <Modal
    title='zip上传'
    wrapClassName="resource-upload-modal"
    open={visible}
    footer={null}
    onCancel={onClose}
    width={550}>
    {/* <Spin tip={`${onlyVideo ? '视频' : '资源'}加载中...`} spinning={modalResourceLoading || contentConfirmLoading}> */}
    <div className="file-wrp">
      <div className="preview">
        <div className="pr-upload-wrapper">
          {(uploadStatus === 'normal' || uploadStatus === 'active') &&
            <div>
            <div>
              <div>{file.name}{ percent === 100 ? t("解压成功") : t("正在解压...")}</div>
              <Progress percent={percent} status={uploadStatus} />
            </div>
              <div>
                <div>{file.name}{ percent2 === 100 ? t('上传成功') : t("正在上传...")}</div>
                {/*Number(*/}
                {/*(taskPanls?.[0]?.progress * 100).toString().match(/^\d+(?:\.\d{0,2})?/),*/}
                <Progress percent={percent2} status={uploadStatus} />
              </div>
            </div>
          }
          { uploadStatus === '' &&
            <Spin spinning={contentConfirmLoading}>
              <Dragger disabled={modalResourceLoading || contentConfirmLoading} {...props}>
                <p className="ant-upload-drag-icon">
                  <PlusIcon />
                </p>
                <p className="ant-upload-text">{t("将文件拖拽至此处，或")}<a>{t("点击上传")}</a></p>
              </Dragger>
              {/*<p className="ant-upload-text"><Checkbox value={synchronize} onChange={(e) => { setSynchronize(e.target.checked) }}>{t("同步到个人资源（若需复用资源，请勾选同步到个人资源，便于下次选用）")}</Checkbox></p>*/}
            </Spin>}
        </div>
      </div>
    </div>
  </Modal>
}
export  default  AddZipModal;
