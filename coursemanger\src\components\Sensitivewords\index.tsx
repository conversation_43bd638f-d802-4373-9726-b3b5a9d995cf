import React, { useEffect } from 'react';
import { Modal, Checkbox } from 'antd';

const SensitiveWords: React.FC = ({}) => {
  const [sensitiveWordsinfo, setSensitiveWordsinfo] = React.useState<any>(null);
  const [visible, setVisible] = React.useState(false);
  const [noMoreRemind, setNoMoreRemind] = React.useState(false);

  // 获取不再提醒的文本数组
  const getNoRemindTexts = (): string[] => {
    const stored = localStorage.getItem('sensitiveWordsNoRemind');
    return stored ? JSON.parse(stored) : [];
  };

  // 保存不再提醒的文本到数组
  const saveNoRemindText = (text: string) => {
    const texts = getNoRemindTexts();
    if (!texts.includes(text)) {
      texts.push(text);
      localStorage.setItem('sensitiveWordsNoRemind', JSON.stringify(texts));
    }
  };

  useEffect(() => {
    // 监听敏感词事件
    const handleSensitiveWords = (event: CustomEvent) => {
      const { content, text, successCallback, failedCallback } = event.detail;
      
      // 检查text是否在不再提醒数组中
      const noRemindTexts = getNoRemindTexts();
      if (noRemindTexts.includes(text)) {
        // 如果在不再提醒列表中，直接执行成功回调
        successCallback?.();
        return;
      }

      setVisible(true);
      setSensitiveWordsinfo({
        text: text,
        content: content || '',
        onOk: successCallback,
        onCancel: failedCallback
      });
    };

    // 添加事件监听器
    window.addEventListener('ShowSensitiveWords', handleSensitiveWords as EventListener);

    // 组件卸载时移除事件监听器
    return () => {
      window.removeEventListener('ShowSensitiveWords', handleSensitiveWords as EventListener);
    };
  }, []);

  // 处理确定按钮点击
  const handleOk = () => {
    if (noMoreRemind && sensitiveWordsinfo.text) {
      // 将当前text添加到不再提醒数组中
      saveNoRemindText(sensitiveWordsinfo.text);
    }
    sensitiveWordsinfo.onOk?.();
    setVisible(false);
    setNoMoreRemind(false); // 重置不再提醒状态
  };

  // 处理取消按钮点击
  const handleCancel = () => {
    sensitiveWordsinfo.onCancel?.();
    setVisible(false);
    setNoMoreRemind(false); // 重置不再提醒状态
  };
    
  return (
    <Modal
      title={'敏感词提醒'}
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      okText="确定"
      cancelText="取消"
    >
      <p>{sensitiveWordsinfo?.content}</p>
      <Checkbox 
        checked={noMoreRemind}
        onChange={(e) => setNoMoreRemind(e.target.checked)}
        style={{ marginTop: '10px' }}
      >
        不再提醒
      </Checkbox>
    </Modal>
  );
};

export default SensitiveWords;
