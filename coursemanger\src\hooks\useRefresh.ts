import {useEffect} from "react";

export const useRefresh = () => {
  useEffect(() => {
    console.log('useRefresh')
  }, []);


  //监听localStorage中的isClosed属性变化
  const storageWatcher = (param: string, mode: any, callback: (param: string, mode: any) => void) => {
    useEffect(() => {
      const handleStorageChange = (e: StorageEvent) => {
        if (e.key === 'isClosed' && e.newValue === 'true') {
          // 当isClosed值变为true时，重新获取列表数据
          console.log('isClosed detected, updating list')
          callback(param, mode);
        }
      };
      //添加storage时间监听器
      window.addEventListener('storage', handleStorageChange)

      //组件卸载的时候移除事件监听器
      return () => {
        window.removeEventListener('storage', handleStorageChange)
      }
    }, []);
  }

  //检查当前窗口的storage值
  const selfStorageWatcher = (param: string, mode: any, callback: (param: string, mode: any) => void) => {
    useEffect(() => {
      const isClosed = window.localStorage.getItem('isClosed');
      if (isClosed === 'true') {
        callback(param, mode);
        window.localStorage.removeItem('isClosed');
      }
    }, []);
  }

  //完成页面操作触发本地存储
  const handleOperate = () => {
    let isClosed = window.localStorage.getItem("isClosed")
    if (isClosed === "true") {
      window.localStorage.removeItem("isClosed")
    }
      window.localStorage.setItem("isClosed","true")
  }


  return {
    storageWatcher,
    selfStorageWatcher,
    handleOperate
  }
};
