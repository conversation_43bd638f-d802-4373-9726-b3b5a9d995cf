
import { Button, Checkbox, Empty, Input, message, Modal, Table, } from 'antd';
import React, { useEffect, useState, } from 'react';
import './CustomgroupingModal.less';
import { CloseOutlined } from '@ant-design/icons';  // 新增删除图标导入
import useLocale from '@/hooks/useLocale';
import { getGroupList, studentList, moveStudents, getGroupDetail, addGroup, deleteGroup } from '@/api/micromajor';
import { set } from 'lodash';
import { IconFont } from '@/components/iconFont';
interface ICustomgroupingModal {
  visible: boolean;
  PubState: string;
  homeworkId: string;
  courseId: string;
  resourceType: string;
  onConfirm: (data: any, curTime?: any) => void;
  onAdd: () => void;
  onclose: () => void;
  disabled?: number[];
  currentname?: string;
  iscoursemap?: boolean;
  videoTotal?: number;
  currentTime?: string;
}

const { Search } = Input;
// 在组件内部添加模拟数据


const CustomgroupingModal: React.FC<ICustomgroupingModal> = props => {
  const { t } = useLocale();
  const {
    visible,
    homeworkId,
    PubState,
    courseId,
    resourceType,
    onAdd,
    onConfirm,
    onclose,
    disabled = [],
    currentname = t('请输入名称'),
    iscoursemap,
    videoTotal,
    currentTime,
  } = props;
  const [selectRows, setSelectRows] = React.useState<any>();
  const [selectRowsList, setSelectList] = React.useState<any>(); // 选中的行
  const [Teamvisible, setTeamvisible] = React.useState(false);//组员 移动 添加
  const [Teamtitle, setTeamtitle] = React.useState(0);// 0 添加组员 1 移动  2 添加小组 
  const [groupName, setGroupName] = React.useState('');// 输入值
  const [groupList, setGroupList] = React.useState<any>([]);  // 小组列表
  const [paramsList, setParams] = React.useState<any>([]);  // 参数
  const [studentListData, setStudentListData] = React.useState<any>([]);  // 学生列表
  const [selectremove, setremove] = React.useState<any>();  // 选中的小组
  const [setcpage, setPage] = React.useState<any>({
    page: 1,
    size: 10,
  });  // 页码
  const [Teamvisiblepage, setTeamvisiblePage] = React.useState<any>({
    page: 1,
    size: 10,
    studentName: '',
  });  // 页码
  const [SelectedTeamRowKeys, setSelectedTeamRowKeys] = useState<any[]>([]);
  const [SelectedTeamTeacher, setSelectedTeamTeacher] = useState<any[]>([]); //组员
  const [TeamvisibleList, setTeamvisibleList] = useState<any[]>([]); //添加组员列表
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedTeacher, setSelectedTeacher] = useState<any[]>([]);
  const [selectedRows, setSelectedRows] = useState<any[]>([]); //单个移动
  const [Teamvtitle, setTeamvtitle] = useState<any>('batch'); //类型  batch 全选 
  const [total, setTotal] = useState(0);
  const [Teamvisibtotal, setTeamvisibTotal] = useState(0);
  //表格
  const columns = [
    {
      title: '组员',
      dataIndex: 'stuName',
      width: '90%',
      render: (stuName: string) => <div>{stuName}</div>,
    },
    {
      title: '操作',
      width: 100,
      render: (_: any, record: any) => (
        <a onClick={() => conremove('one', record)}>{t("移动")}</a>
      ),
    },
  ];
  const Teamcolumns = [
    {
      title: '组员',
      dataIndex: 'stuName',
      width: '90%',
      render: (stuName: string) => <div>{stuName}</div>,
    },
    {
      title: '当前分组',
      width: 150,
      dataIndex: 'teamName',
      render: (teamName: string) => <div>{teamName}</div>,
    },

  ];
  useEffect(() => {

    if (!homeworkId) return; // 无作业ID时直接返回
    console.log(homeworkId, 'homeworkId');
    var params = {
      homeId: props.homeworkId,  // 作业id
      courseId: props.courseId,  // 课程id
      resourceType: props.resourceType,  // 资源类型 1 视频 2 文档 3 音频 4 图片 5 其他
    }
    setParams(params);
    getGroupListOk(params)
  }, [homeworkId]);

  useEffect(() => {
    if (!homeworkId) return; // 无作业ID时直接返回
    var params = {
      homeId: props.homeworkId,  // 作业id
      teamId: selectRows,  // 小组id
      size: setcpage.size,  // 课程id
      page: setcpage.page,  // 资源类型 1 视频 2 文档 3 音频 4 图片 5 其他
    }
    getGroupDetailOk(params)
  }, [setcpage, homeworkId]);

  useEffect(() => {
    if (!homeworkId) return; // 无作业ID时直接返回
    var params = {
      homeId: props.homeworkId,  // 作业id
      teamId: selectRows,  // 小组id
      studentName: Teamvisiblepage.studentName ? Teamvisiblepage.studentName : '',//名称
      size: Teamvisiblepage.size,  // 课程id
      page: Teamvisiblepage.page,  // 资源类型 1 视频 2 文档 3 音频 4 图片 5 其他
    }
    studentListOk(params)
  }, [Teamvisiblepage, homeworkId]);

  // 获取作业分组的组列表
  const getGroupListOk = (params: any) => {
    getGroupList(params).then(res => {
      if (res.status === 200) {
        setGroupList(res.data);
        setSelectRows(selectRowsList ? selectRowsList?.teamId : res.data[0].teamId);
        setSelectList(res.data[0])
        console.log(selectRowsList, 'selectRowsList');
        var params = {
          homeId: props.homeworkId,  // 作业id
          teamId: selectRowsList ? selectRowsList?.teamId : res.data[0].teamId,  // 小组id
          size: setcpage.size,  // 课程id
          page: setcpage.page,  // 资源类型 1 视频 2 文档 3 音频 4 图片 5 其他
        }
        getGroupDetailOk(params)
      }
    }).catch(() => {
    })
  }
  //点击添加按钮查询得人员列表(所有的)
  const studentListOk = (params: any) => {
    studentList(params).then(res => {
      if (res.status === 200) {
        setTeamvisibleList(res?.data?.data);  // 学生列表
        console.log(res?.data?.totalCount, 'totalCount');

        setTeamvisibTotal(res?.data?.totalCount);  // 学生列表总数
      }
    }).catch(() => {
    })
  }

  // 添加小组
  const addGroupOk = (params: {
    homeId: string; // 作业id
    courseId: string; // 课程id
  }) => {
    addGroup(params).then(res => {
      if (res.status === 200) {
        getGroupListOk(paramsList)
        message.success('添加成功');
      } else {
        message.success('添加失败');
      }
    }).catch(() => {
    })
  }


  //删除
  const deleteGroupOk = (params: any) => {
    deleteGroup(params).then(res => {
      if (res.status === 200) {
        message.success('删除成功');
        getGroupListOk(paramsList)
      } else {
        message.success('删除失败');
      }
    }).catch(() => {
    })
  }

  // 移动小组
  const moveStudentsOk = (params: any) => {
    moveStudents(params).then(res => {
      if (res.status === 200) {
        message.success('移动成功');
        getGroupListOk(paramsList)
      } else {
        message.success('移动失败');
      }
      setremove('')
      setSelectedRows([])
      setSelectedTeamRowKeys([])
      setSelectedTeamTeacher([])
      setSelectedRowKeys([])
      setSelectedTeacher([])
    }).catch(() => {
    })
  }

  // 获取分组学生详情 
  const getGroupDetailOk = (params: any) => {
    getGroupDetail(params).then(res => {
      if (res.status === 200) {
        setStudentListData(res?.data?.data);
        setTotal(res?.data?.totalCount);
      }
    }).catch(() => {
    })
  }

  // 添加删除处理函数 删除小组
  const handleDeleteGroup = (groupId: any) => {
    if (PubState === 'pub') return;
    var params = {
      homeId: props.homeworkId,  // 作业id
      courseId: props.courseId,  // 课程id
      teamName: groupId.teamName,  // 小组名称
      groupIndex: groupId.groupIndex,  // 小组索引,
      teamId: groupId.teamId,  // 小组id
    }
    deleteGroupOk(params)
  };

  // 选中小组
  const handleselctGroup = (groupId: number, item: any) => {
    setSelectRows(groupId);
    setSelectList(item)
    var params = {
      homeId: props.homeworkId,  // 作业id
      teamId: groupId,  // 小组id,
    }
    getGroupDetailOk(params)
  };


  const conremove = (title: string, record: any) => {  //移动
    setSelectedRows(prev => [record]);
    setTeamvtitle(title)
    if (groupList.length <= 1) {
      message.warning('请添加小组');
      return;
    }
    setTeamvisible(true);
    setTeamtitle(1);
  }

  const Addbuild = (title: string) => {  //新建小组
    console.log('移动', title);
    if (title == '新建小组') {
      var params = {
        homeId: props.homeworkId,  // 作业id
        courseId: props.courseId,  // 课程id
      }
      addGroupOk(params);
    }
    // setTeamvisible(true);  // 不需要打开弹窗
    // setTeamtitle(2);
  }

  const Addteam = (title: string) => {  //添加组员
    var params1 = {
      homeId: props.homeworkId,  // 作业id
      teamId: selectRows,  // 小组id
      studentName: Teamvisiblepage.studentName,//名称
      size: Teamvisiblepage.size,  // 课程id
      page: Teamvisiblepage.page,  // 资源类型 1 视频 2 文档 3 音频 4 图片 5 其他
    }
    studentListOk(params1)
    setSelectedTeamRowKeys([])
    setTeamvisible(true);
    setTeamtitle(0);
  }

  const onSearch = (title: string) => {  //搜索
    setTeamvisiblePage({
      studentName: title,
      page: 1,
      size: 10,
    })
  }



  const TeamonOk = () => {  //确认 / 0 添加组员 1 移动  2 添加小组 
    setTeamvisible(false);
    if (Teamtitle === 1) {  // 移动
      var params = {
        homeId: props.homeworkId,
        groupIndex: selectremove.groupIndex,
        teamName: selectremove.teamName,
        teamId: selectremove.teamId,
        selectStudent: Teamvtitle === 'one' ? selectedRows : selectedTeacher  // one单个移动 /全选移动
      }
      moveStudentsOk(params);
    } else if (Teamtitle === 0) {  //
      if (SelectedTeamTeacher.length == 0) {
        message.warning('请选择要添加的组员');
        return;
      }
      // if (selectRowsList.groupIndex == -1) {
      //   message.warning('不能添加到未分组的小组');
      //   return;
      // }

      var params = {
        homeId: props.homeworkId,
        groupIndex: selectRowsList.groupIndex,
        teamName: selectRowsList.teamName,
        teamId: selectRowsList.teamId,
        selectStudent: SelectedTeamTeacher  // one单个移动 /全选移动
      }

      moveStudentsOk(params);
    }
    setTeamvisible(false);
    setGroupName('');  // 关闭弹窗时清空输入
  }


  const confirm = () => {
    onConfirm(groupList);
    if (groupList.length > 1) {

      message.success('分组成功');
    }
    close();
  };

  const close = () => {
    if (groupList) {
      onConfirm(groupList);
    }
    onclose();
  };

  return (
    <div>
      <Modal
        title='小组管理'
        visible={visible}
        onCancel={close}
        width={800}
        // zIndex={1002}
        destroyOnClose
        className="Custom_modal"
        footer={[
          <Button
            type="primary"
            key="1"
            onClick={confirm}
          // disabled={selectRows.length === 0}
          >
            {t('确定')}
          </Button>,
          <Button key="2" onClick={close}>
            {t('取消')}
          </Button>,
        ]}
      >
        <div className="Custom-box">
          <div className="btn-box">
            <div className='box-left' >
              <div className='left-list' >
                {groupList.map((group: { teamId: any; teamName: any; teamNums: any; groupIndex: any }) => (
                  <div key={group?.teamId} className={`group-item ${selectRows === group.teamId ? 'active' : ''}`} onClick={() => handleselctGroup(group.teamId, group)} >
                    <span>{group.teamName}({group?.teamNums})</span>

                    {
                      group.groupIndex != -1 && selectRowsList?.teamId == group.teamId ?
                        <div>
                          {PubState !== 'pub' && (
                            <IconFont type='icondelete2' onClick={() => { handleDeleteGroup(group) }} />
                          )}
                        </div>
                        :
                        ''
                    }

                  </div>
                ))}
              </div>
              <Button disabled={PubState === 'pub'} onClick={() => Addbuild('新建小组')} style={{ marginLeft: '25%' }} type='primary' ghost>{t("新建小组")}</Button>
            </div>
            <div className='box-right' >
              <div className='right-list' >
                <div className='right-header' >
                  <Button disabled={PubState === 'pub'} onClick={() => Addteam('')} type='primary' ghost>{t("添加组员")}</Button>
                  <Button disabled={selectedTeacher?.length == 0 || PubState === 'pub'} onClick={() => conremove('batch', '')} style={{ margin: '10px' }} type='primary' ghost>{t("移动")}</Button>
                </div>
                <Table
                  scroll={{ y: 240 }}
                  rowSelection={{
                    type: 'checkbox',
                    selectedRowKeys,
                    onSelectAll: (selected, selectedRows) => {
                      setSelectedRowKeys(selected ? selectedRows.map(r => r) : []);
                      setSelectedTeacher(selectedRows);
                    },
                    onChange: (selectedRowKeys, selectedRows) => {
                      setSelectedRowKeys(selectedRowKeys);
                      setSelectedTeacher(selectedRows);
                    }
                  }}
                  pagination={{
                    current: setcpage.page,
                    pageSize: setcpage.size,
                    total,
                    showSizeChanger: true,
                    pageSizeOptions: ['10', '20', '40', '60'],
                    showTotal: (total: any) => `共 ${total} 人`,
                    onChange: (page: number, size: number) => {
                      setPage({ ...setcpage, page, size });
                      setSelectedRowKeys([]); // 翻页清空选择
                    }
                  }}
                  rowKey={'id'}
                  dataSource={studentListData}
                  columns={columns}
                />
              </div>
            </div>
          </div>
        </div>
      </Modal>
      <Modal
        title={Teamtitle === 0 ? '添加组员' : Teamtitle === 1 ? '移动到' : '添加小组'}
        visible={Teamvisible}
        zIndex={1002}
        onCancel={() => {
          setTeamvisible(false);
          setTeamvisiblePage({
            studentName: '', // 这里应该清空搜索条件
            page: 1,
            size: 10,
          });
        }}
        onOk={() => TeamonOk()}
        width={Teamtitle === 0 ? 600 : Teamtitle === 1 ? 300 : 500}
      >
        <div>
          {
            Teamtitle === 0 && <div>
              <Search placeholder="请输入名字" onSearch={(value) => onSearch(value)} />
              <Table
                style={{ marginTop: '10px' }}
                scroll={{ y: 240 }}
                rowSelection={{
                  type: 'checkbox',
                  selectedRowKeys: SelectedTeamRowKeys,
                  onSelectAll: (selected, selectedRows) => {
                    setSelectedTeamRowKeys(selected ? selectedRows.map(r => r) : []);
                    setSelectedTeamTeacher(selectedRows);
                  },
                  onChange: (SelectedTeamRowKeys, selectedRows) => {
                    setSelectedTeamRowKeys(SelectedTeamRowKeys);
                    setSelectedTeamTeacher(selectedRows);
                  }
                }}
                pagination={{
                  current: Teamvisiblepage.page,
                  pageSize: Teamvisiblepage.size,
                  total: Teamvisibtotal,
                  showSizeChanger: true,
                  pageSizeOptions: ['10', '20', '40', '60'],
                  showTotal: (Teamvisibtotal: any) => `共 ${Teamvisibtotal} 人`,
                  onChange: (page: number, size: number) => {
                    setTeamvisiblePage({ ...Teamvisiblepage, page, size });
                    setSelectedTeamRowKeys([]); // 翻页清空选择
                  }
                }}
                rowKey={'id'}
                dataSource={TeamvisibleList}
                columns={Teamcolumns}
              />
            </div>
          }
          {
            Teamtitle === 1 && <div>
              {groupList.map((group: { teamId: any; teamName: any; teamNums: any; groupIndex: any }) => (
                group.groupIndex != -1 && (
                  <div key={group?.teamId} style={{
                    maxHeight: '300px',
                    overflowY: 'auto',
                    padding: '7px',
                    cursor: 'pointer',
                    marginTop: '6px 0',
                    borderRadius: '4px',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '8px', // 调整间距
                    backgroundColor: selectremove?.teamId === group.teamId ? '#eef5ff' : undefined,
                  }} >
                    <div onClick={() => {
                      // selectremove, setremove
                      setremove(group)
                    }} >{group.teamName}({group?.teamNums})</div>
                  </div>
                )
              ))}
            </div>
          }
          {Teamtitle === 2 &&
            <Input
              value={groupName}
              onChange={(e) => setGroupName(e.target.value)}
              placeholder="请输入小组名"
            />}
        </div>
      </Modal>
    </div>
  );
};

export default CustomgroupingModal;
