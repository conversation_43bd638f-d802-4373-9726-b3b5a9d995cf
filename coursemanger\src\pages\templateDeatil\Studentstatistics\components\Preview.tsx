/*
 * @Author: 李晋
 * @Date: 2021-11-10 14:18:37
 * @Email: <EMAIL>
 * @LastEditTime: 2021-11-17 11:03:51
 * @Description: file information
 * @Company: Sobey
 */
import { Modal, Spin, Button, message, Tabs, Checkbox, Space, Radio, RadioChangeEvent, Switch, Tooltip, Image } from 'antd';
import React, { FC, useEffect, useRef, useState, useImperativeHandle, forwardRef, createContext } from 'react';
import Entity from '@/components/entity/entity';
import chapterApis from '@/api/chapter';
import './index.less';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import TopicItem from '@/pages/HomeworkManagement/components/TopicItem';
import TopicSelectModal from '@/pages/HomeworkManagement/components/TopicSelectModal';
import { IGlobalModelState, useSelector, useLocation, useHistory } from 'umi';
import { useInfiniteScroll } from 'ahooks';
import { getTopicByIds } from '@/api/homework';
import { optionType_ } from '@/pages/HomeworkManagement/utils/columns';
import TopicTypeSelectModal from '@/pages/HomeworkManagement/components/TopicTypeSelectModal';
import useLocale from '@/hooks/useLocale';
export const videoContext = createContext({ topics: [] });
interface TpResult {
  list: any[];
  total: number;
  pageIndex: number;
}
interface ResourcePreviewModalProps {
  modalVisible: boolean;
  modalClose: (isRefresh?: boolean) => void;
  showKnowledge?: boolean;
  onAddTopic?: (type: number) => void;
  resource: {
    id: string;
    type: string;
    name: string;
    knowledge?: any;
    sourceId?: string;
  };
  className?: string;
  sourceId?: any;
  type?: "course" | "template";
}
const addZero = (value: number) => `${value > 9 ? value : `0${value}`}`;
const dealTime = (time: number) => {
  if (time < 60) {
    return `00:00:${addZero(Math.ceil(time))}`;
  } else if (time >= 60 && time < 60 * 60) {
    const min = Math.floor(time / 60);
    const seconds = Math.ceil(time - min * 60);
    return `00:${addZero(min)}:${addZero(seconds)}`;
  } else if (time >= 3600) {
    const hour = Math.floor(time / 60);
    const min = Math.floor(time - hour * 60);
    const seconds = Math.floor(time - min * 60);
    return `${addZero(hour)}:${addZero(min)}:${addZero(seconds)}`;
  }
};
const ResourcePreviewModal: FC<ResourcePreviewModalProps> = forwardRef(({
  modalVisible,
  modalClose,
  onAddTopic,
  showKnowledge,
  resource,
  className,
  type = "course"
}, ref) => {
  useImperativeHandle(ref, () => ({
    handleConfirmSelectTp
  }));

  const { t } = useLocale();
  const location: any = useLocation();
  const { query }:any = useLocation();
  const { parameterConfigObj } = useSelector<{global: IGlobalModelState;}, IGlobalModelState>((state) => state.global);
  const [previewEntity, setPreviewEntity] = useState<any>(null);
  const [entityLoading, setEntityLoading] = useState<boolean>(false);
  const [knowledgeList, setKnowledgeList] = useState<any>([]);
  const [showEdit, setShowEdit] = useState<boolean>(false);
  const [tabKey, setTabKey] = useState<string>(parameterConfigObj.zyzx?.includes('enable_interactive_question') ? "1" : "2");
  const [checkedList, setCheckedList] = useState<CheckboxValueType[]>([]);
  const [indeterminate, setIndeterminate] = useState<boolean>(false);
  const [checkAll, setCheckAll] = useState<boolean>(false);
  const [tpVisible, setTpVisible] = useState<boolean>(false);
  const [tpSelectVis, setTpSelectVis] = useState<boolean>(false);
  const [topicTypeSelectVisible, setTopicTypeSelectVisible] = useState<boolean>(false);
  const [selectTpType, setSelectTpType] = useState<"checkbox" | "radio">("checkbox");
  // const [topicType, setTopicType] = useState<number>(0);
  const [currentTp, setCurrentTp] = useState<any>({});
  const { courseDetail, templateCourseDetail } = useSelector<Models.Store, any>(
  (state) => state.moocCourse);

  const [totalTime, setTotalTime] = useState<number>(0);
  const [currentTime, setCurrentTime] = useState<number>(0);
  const [currentTimeString, setCurrentTimeString] = useState<string>('');
  const [versionName, setVersionName] = useState<any>();
  const [versionList, setVersionList] = useState<any[]>([]);
  const [versionVis, setVersionVis] = useState<boolean>(false);
  const [version, setVersion] = useState<string>(""); // 弹窗中用
  const [tpOn, setTpOn] = useState<boolean>(true);
  const tpRef = useRef<HTMLDivElement>(null);
  const [pageChange, setPageChange] = useState<boolean>(false);
  const { data, loading, loadingMore, noMore, reload } = useInfiniteScroll(
  (d: any) => {
    // debugger
    return new Promise((resolve) => {
      chapterApis.getVideoTopics({
        pageIndex: Number(d?.pageIndex ?? 0) + 1,
        pageSize: 10,
        resourceId: resource.id,
        sourceId: resource.sourceId
      }).then((res: any) => {
        if (res.success) {
          const _list = res.data.data ?? [];
          if (d?.pageIndex === 1 && _list.length === 0) {
            getCurVersion();
          }
          getTopicByIds(_list.map((item: any) => item.questionId)).then((_res: any) => {
            if (_res.status === 200) {
              const list = _list.map((item: any) => ({
                ...item,
                ...(_res.data?.find((cur: any) => cur.id === item.questionId) ?? {})
              }));
              resolve({
                pageIndex: res.data.pageIndex,
                list,
                total: res.data.recordTotal
              });
            }
          });
        } else {
          message.error("查询失败");
        }
      });
    });

  },
  {
    target: tpRef,
    isNoMore: (d: any) => d?.total <= d?.list?.length,
    manual: true,
    threshold: 0
  });

  // const getTpList = (page: number): Promise<TpResult> =>

  const initCheckbox = () => {
    setCheckAll(false);
    setIndeterminate(false);
    setCheckedList([]);
  };

  const handleCheckedChange = (list: CheckboxValueType[]) => {
    setCheckedList(list);
    setIndeterminate(!!list.length && list.length < data.list.length);
    setCheckAll(list.length === data.list.length);
  };

  const onCheckAllChange = (e: CheckboxChangeEvent) => {
    setCheckedList(e.target.checked ? data.list.map((item: any) => item.uniqueId) : []);
    setIndeterminate(false);
    setCheckAll(e.target.checked);
  };
  useEffect(() => {
    if (resource) {
      onEntityPreview(resource.id, {
        name: resource.type,
        type: resource.type,
        knowledge: resource.knowledge
      });
      if (showKnowledge && resource.type == 'video') {
        getKnowledgeList();
        getCurVersion();
        getVersionLs();
        getTpStatus();
        reload();
      } else {
        setKnowledgeList([]);
      }
    }
  }, [resource]);
  useEffect(() => {
    if (versionName) {
      reload();
    }
  }, [versionName]);
  /**
   * resource资源对象中取资源url
   *
   * @param {*} resource
   * @return {*}
   */
  const getPreviewPath = (resource: any) => {
    if (resource && resource.fileGroups) {
      const { fileGroups } = resource;
      if (resource.type.includes("document")) {
        const path = fileGroups.filter(
        (file: any) => file.typeCode === 'sourcefile')?.[
        0]?.fileItems?.[0]?.displayPath;
        return path;
      }
      const previewFile = fileGroups.filter(
      (file: any) => file.typeCode === 'previewfile');

      const videogroupFile = fileGroups.filter(
      (file: any) => file.typeCode === 'sourcefile');

      return previewFile.length > 0 ?
      previewFile[0].fileItems[0].filePath :
      videogroupFile[0].fileItems[0].filePath;
    }
    return '';
  };
  /**
   * 查询资源预览网址，显示资源详情modal
   *
   * @param {string} id
   * @param {*} { name, type }
   */
  const onEntityPreview = (id: string, { name, type, knowledge }: any) => {
    setEntityLoading(true);
    chapterApis.
    resourceDetail(id, {
      courseId: query.id,
      courseSemester: query.sm
    }).
    then((res) => {
      if (res.success) {
        if (res.data.canEdit) {
          setShowEdit(true);
        } else {
          setShowEdit(false);
        }
        setPreviewEntity({
          src: getPreviewPath(res.data),
          name,
          type: res.data.type.split('_')[2],
          knowledge
        });
        // setEntityModalVisible(true);
      }
    }).
    finally(() => setEntityLoading(false));
  };

  const editHandle = () => {
    window.open(`/rman/#/basic/rmanDetail/${resource.id}`);
  };
  const getKnowledgeList = () => {
    chapterApis.getKnowledgePoint(resource.id).then((res: any) => {
      if (res.success) {
        setKnowledgeList(res.data.data?.[0]?.metadata || []);
      }
    });
  };
  const handleDeleteKnowledge = (guid: any) => {
    Modal.confirm({
      content: t("确定要删除该知识点吗？"),
      zIndex: 20000,
      onOk() {
        chapterApis.deleteKnowledgePoint(resource.id, guid).then((res: any) => {
          if (res.success) {
            getKnowledgeList();
            message.success("删除成功");
          } else {
            message.error("删除失败");
          }
        });
      }
    });
  };
  const getVersionLs = () => {
    chapterApis.getVideoTopicVersion({
      pageIndex: 1,
      pageSize: 99,
      resourceId: resource.id,
      sourceId: resource.sourceId,
    }).then((res: any) => {
      if (res.success) {
        setVersionList(res.data.data);
      }
    });
  };
  const getCurVersion = () => {
    chapterApis.reqCurVersion({ sourceId: resource.sourceId, resourceId: resource.id }).then((res: any) => {
      if (res.success) {
        setVersionName(res.data);
      }
    });
  };
  const handleConfirmSelectTp = (questions: any, pointIn: number) => {
    const isAdd = selectTpType === "checkbox";
    const defaultP = {
      sourceId: resource.sourceId,
      courseName: type === "course" ? courseDetail.name : templateCourseDetail?.entityData?.name,
      resourceId: resource.id,
      pointIn: pointIn * Math.pow(10, 7),
      versionName: versionName?.code ? versionName?.code : null
    };
    const param = isAdd ? { ...defaultP, questionIds: questions.map((question: any) => Number(question.id)), courseId: location.query.id } : {
      ...defaultP,
      uniqueId: currentTp.uniqueId,
      questionId: questions[0].id
    };
    const func = isAdd ? chapterApis.addVideoTopic : chapterApis.replaceVideoTopic;
    func(param).then((res: any) => {
      if (res.success) {
        message.success(`${isAdd ? t("添加") : t("替换")}${t("成功")}`);
        setPageChange(true);
        if (!versionName?.code) {
          handleUseVersion(res.data?.code).then((success: boolean) => {
            if (success) {
              setVersionName(res.data);
            }
          });
          getVersionLs();
        } else {
          reload();
        }
        setTpSelectVis(false);
      } else {
        message.error(`${isAdd ? t("添加") : t("替换")}${t("失败")}`);
      }
    });
  };
  const handleUseVersion = (version: string) => {
    return chapterApis.useVersion({
      sourceId: resource.sourceId,
      versionName: version,
      oldVersionName: versionName?.code ?? null,
      resourceId: resource.id
    }).then((res: any) => {
      setPageChange(true);
      if (res.success) {
        return true;
      } else {
        return false;
      }
    });
  };
  const handleAddTp = () => {
    setSelectTpType("checkbox");
    setTpSelectVis(true);
    setCurrentTimeString(dealTime(currentTime) ?? "");
  };
  const handleDeleteTp = (cur?: string) => {
    let selected = [];
    if (!cur) {
      if (checkedList.length < 0) {
        message.warning("请选择要删除的题目！");
        return;
      }
      selected = checkedList;
    } else {
      selected = [cur];
    }
    chapterApis.deleteVideoTopic(selected).then((res: any) => {
      if (res.success) {
        setPageChange(true);
        message.success("删除成功");
        reload();
        initCheckbox();
      } else {
        message.error("删除失败");
      }
    });
  };
  const getTpStatus = () => {
    chapterApis.reqTopicDisabled({
      resourceId: resource.id,
      sourceId: resource.sourceId
    }).then((res: any) => {
      if (res.success) {
        setTpOn(!res.data);
      }
    });
  };
  const updateTpStatus = (isOn: boolean) => {
    chapterApis.disabledTopic({
      resourceId: resource.id,
      sourceId: resource.sourceId,
      isDisable: !isOn
    }).then((res: any) => {
      if (res.success) {
        setPageChange(true);
        message.success("设置成功");
        setTpOn(isOn);
      } else {
        message.error("设置失败");
      }
    });
  };
  return modalVisible ?
    <div>
      <Spin spinning={entityLoading}>
        <div className='box'>
          <div className="entity-preview" style={knowledgeList?.length == 0 ? { width: '80%' } : {}}>
            <div className={`video-wrap ${previewEntity?.type === 'document' ? 'doc-wrp' : ''}`}>
              {previewEntity ?
            <videoContext.Provider value={{ topics: data?.list ?? [] }}>
                  <Entity isAutoplay type={previewEntity?.type} src={previewEntity?.src} id={"previewVideo"} knowledge={previewEntity?.knowledge} onUpdate={(currentTime: number, totalTime?: number) => {
                setCurrentTime(currentTime);
                if (totalTime) setTotalTime(totalTime);
              }} />
                </videoContext.Provider> :
            <div style={{ height: 375 }} />}
            </div>
          </div>
        </div>
      </Spin>
      <Modal open={tpVisible} footer={null} zIndex={1002} onCancel={() => setTpVisible(false)}>
        <TopicItem data={currentTp} />
      </Modal>
      <Modal
    title={t("选择历史版本")}
    zIndex={1003}
    open={versionVis}
    onCancel={() => setVersionVis(false)}
    onOk={() => {
      if (version !== versionName) {
        handleUseVersion(version).then((success) => {
          setVersionName(versionList.find((item: any) => item.code === version));
          message.success("设置成功！");
        });
      }
      setVersionVis(false);
    }}>

        <Radio.Group value={version} onChange={(e: RadioChangeEvent) => setVersion(e.target.value)}>
          {versionList.map((version: any) => <div><Radio value={version.code} key={version.code}>{version.name}</Radio></div>)}
        </Radio.Group>
      </Modal>
      {tpSelectVis&&<TopicSelectModal
    visible={tpSelectVis}
    type={selectTpType}
    disabled={[3, 2]}
    onConfirm={handleConfirmSelectTp}
    onAdd={() => setTopicTypeSelectVisible(true)}
    videoTotal={totalTime}
    onclose={() => setTpSelectVis(false)}
    currentTime={currentTimeString} />}

      <TopicTypeSelectModal
    exclude={[3, 2]}
    visible={topicTypeSelectVisible}
    onClose={() => setTopicTypeSelectVisible(false)}
    onConfirm={(type: number) => {
      onAddTopic?.(type);
      setTpSelectVis(false);
    }} />
    </div> :
  null;
});

export default ResourcePreviewModal;
