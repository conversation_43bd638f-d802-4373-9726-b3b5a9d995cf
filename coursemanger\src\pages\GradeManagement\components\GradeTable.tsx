import React, { useEffect, useState } from 'react';
import { Table, Input, message } from 'antd';
import axios from 'axios';
import { GradeManageService } from '@/api/GradeManageService';
const { Search } = Input;

const columns = [
  { title: '姓名', dataIndex: 'name', key: 'name', align: 'center' },
  { title: '学号', dataIndex: 'studentId', key: 'studentId', align: 'center' },
  { title: '学院', dataIndex: 'college', key: 'college', align: 'center' },
  { title: '专业', dataIndex: 'major', key: 'major', align: 'center' },
  {
    title: (
      <div>
        课件学习进度
        <br />
        {/*<span style={{ fontWeight: 'normal' }}>观看页数/总页数</span>*/}
      </div>
    ),
    dataIndex: 'courseworkProgressShow',
    key: 'courseworkProgressShow',
    align: 'center',
    render: (text, record) => {
      return text + '%'
    }
  },
  {
    title: (
      <div>
        作业提交
        <br />
        {/*<span style={{ fontWeight: 'normal' }}>提交数（按作业关联的试题、操作作业实时同环分室）/总数</span>*/}
      </div>
    ),
    dataIndex: 'homeworkPercentage',
    key: 'homeworkPercentage',
    align: 'center',
    render: (text, record) => {
      return text + '%'
    }
  },
  {
    title: (
      <div>
        考试提交
        <br />
        {/*<span style={{ fontWeight: 'normal' }}>提交数/总数</span>*/}
      </div>
    ),
    dataIndex: 'examPercentage',
    key: 'examPercentage',
    align: 'center',
    render: (text, record) => {
      return text + '%'
    }
  },
  {
    title: (
      <div>
        出勤情况
        <br />
        {/*<span style={{ fontWeight: 'normal' }}>出勤总数（计算单个用户“是否出勤”签到数）/签到总数</span>*/}
      </div>
    ),
    dataIndex: 'attendance',
    key: 'attendance',
    align: 'center',
    render: (text, record) => {
      return text + '%'
    }
  },
  {
    title: (
      <div>
        弹幕次数
        <br />
        {/*<span style={{ fontWeight: 'normal' }}>该用户该课程弹幕次数之和</span>*/}
      </div>
    ),
    dataIndex: 'bulletScreenCount',
    key: 'bulletScreenCount',
    align: 'center',
  },
  {
    title: (
      <div>
        投稿次数
        <br />
        {/*<span style={{ fontWeight: 'normal' }}>该用户该课程投稿次数之和</span>*/}
      </div>
    ),
    dataIndex: 'submissionCount',
    key: 'submissionCount',
    align: 'center'
  },
  {
    title: (
      <div>
        主题数
        <br />
        {/*<span style={{ fontWeight: 'normal' }}>该用户该课程主题数之和</span>*/}
      </div>
    ),
    dataIndex: 'mainPostCount',
    key: 'mainPostCount',
    align: 'center'
  },
  {
    title: (
      <div>
        评论数
        <br />
        {/*<span style={{ fontWeight: 'normal' }}>该用户该课程评论数之和</span>*/}
      </div>
    ),
    dataIndex: 'commentCount',
    key: 'commentCount',
    align: 'center'
  },
  {
    title: (
      <div>
        回复数
        <br />
        {/*<span style={{ fontWeight: 'normal' }}>该用户该课程回复数之和</span>*/}
      </div>
    ),
    dataIndex: 'replyCount',
    key: 'replyCount',
    align: 'center'
  },
  {
    title: (
      <div>
        其他考核
        <br />
        {/*<span style={{ fontWeight: 'normal' }}>如果其它科科学没有考不考核</span>*/}
      </div>
    ),
    dataIndex: 'otherAssessment',
    key: 'otherAssessment',
    align: 'center'
  }
];

const defaultParams = {
  courseId: '', // 这里填你的课程ID
  courseSemester: 0,
  courseType: 0,
  keyWord: '',
  page: 1,
  size: 10
};

const GradeTable = ({ courseId,courseSemester, courseType}) => {
  console.log(courseId, courseSemester, courseType);
  const [data, setData] = useState([]);
  const [params, setParams] = useState({
    courseId,
    courseSemester,
    courseType,
    keyWord: '',
    page: 1,
    size: 10
  });
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);

  const fetchData = async (customParams = {}) => {
    setLoading(true);
    try {
      const res = await GradeManageService.getThirdList( {
        ...params,
        ...customParams
      });
      // 假设返回格式为 { data: { list: [], total: 0 } }
      setData(res.data.data.records || []);
      setTotal(res.data.data.total || 0);
      setParams({
        ...params,
        page: res.data.data.pageNum,
        size: res.data.data.pageSize,
      })
    } catch (e) {
      message.error('数据获取失败');
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchData();
  }, [params.page, params.size]);

  const onSearch = (value) => {
    setParams({ ...params, keyWord: value, page: 1 });
    fetchData({ keyWord: value, page: 1 });
  };

  const onChange = (pagination: any) => {
    setParams({ ...params, page: pagination.current, size: pagination.pageSize });
  };

  return (
    <div style={{ padding: 20, background: '#fff', minHeight: '100vh' }}>
      <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: 16 }}>
        <Search
          placeholder="请输入姓名/学号"
          onSearch={onSearch}
          style={{ width: 240 }}
          allowClear
        />
      </div>
      <Table
        columns={columns}
        dataSource={data}
        rowKey={(record, idx) => record.id || idx}
        loading={loading}
        pagination={{
          current: params.page,
          pageSize: params.size,
          total,
          showTotal: (t) => `共${t}条`
        }}
        onChange={onChange}
        bordered
        scroll={{ x: 'max-content' }}
      />
    </div>
  );
};

export default GradeTable;
