import React, { useEffect, useState } from 'react';
import { TreeSelect, Form } from 'antd';
import type { TreeSelectProps } from 'antd';
import baseInfo from '@/api/baseInfo'; // 请根据实际API路径调整

interface SchoolSelectProps {
  message?: string;
  name: string;
  label: string;
  multiple?: boolean;
}

interface TreeNode {
  title: string;
  value: string;
  children?: TreeNode[];
  isLeaf?: boolean;
}

const SchoolSelect: React.FC<SchoolSelectProps> = ({
  message = '请选择学院和专业',
  name,
  label,
  multiple = false,
}) => {
  const [treeData, setTreeData] = useState<TreeNode[]>([]);
  const [loading, setLoading] = useState(false);

  // 加载学院列表
  const loadSchoolData = async () => {
    try {
      setLoading(true);
      const response = await baseInfo.getSchoolList();
      const schools = response.extendMessage.map((school: any) => ({
        title: school.organizationName,
        value: school.organizationCode,
        isLeaf: false,
      }));
      setTreeData(schools);
    } catch (error) {
      console.error('加载学院列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 加载专业列表
  const loadMajorData = async (schoolId: string) => {
    try {
      const response = await baseInfo.getMajorList(schoolId);
      const majors = response.extendMessage.map((major: any) => ({
        title: major.extend.organizationNickname || major.organizationName,
        value: schoolId + ',' + major.organizationCode,
        isLeaf: true,
      }));
      return majors;
    } catch (error) {
      console.error('加载专业列表失败:', error);
      return [];
    }
  };

  // 处理树节点展开
  const onLoadData: TreeSelectProps['loadData'] = async (node) => {
    if (node.value) {
      const majors = await loadMajorData(node.value.toString());
      setTreeData((origin) =>
        origin.map((item) => {
          if (item.value === node.value) {
            return {
              ...item,
              children: majors,
            };
          }
          return item;
        })
      );
    }
  };

  useEffect(() => {
    loadSchoolData();
  }, []);

  return (
    <Form.Item
      name={name}
      label={label}
      style={{width:'180px'}}      
    >
      <TreeSelect 
        treeData={treeData}
        loadData={onLoadData}
        multiple={multiple}
        treeNodeFilterProp="title"
        placeholder={message}
        loading={loading}
        allowClear
        showSearch
        style={{ width: '100%' }}
      />
    </Form.Item>
  );
};

export default SchoolSelect;
